import {
  AbilityConstant,
  common,
  Configuration,
  UIExtensionAbility,
  UIExtensionContentSession,
  Want
} from '@kit.AbilityKit';
import {
  STORAGE_CALL_ASSISTANT_PRIVACY_PAGE_TYPE
} from '@callassistant/callassistant/src/main/ets/constants/CallAssistantConstants'
import { isDebugOn, VaLog } from '@callassistant/callassistant/src/main/ets/util/VaLog';
import { GlobalPreloadUtil } from '@voicefix/base/src/main/ets/util/GlobalPreloadUtil';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import { SystemSetting } from '@hms-assistant/common-storage/src/main/ets/settings/SystemSetting';
import { settings } from '@kit.BasicServicesKit';

const TAG: string = 'CallAssistantCallLogAbility';

export default class CallAssistantCallLogAbility extends UIExtensionAbility {
  public static globalContext: common.UIExtensionContext | null = null;

  onCreate(launchParam: AbilityConstant.LaunchParam): void {
    VaLog.debug(TAG, `onCreate ` + JSON.stringify(launchParam));
  }

  onForeground(): void {
    VaLog.info(TAG, `CallAssistantCallLogAbility onForeground`);
  }

  onBackground(): void {
    VaLog.info(TAG, `CallAssistantCallLogAbility onBackground`);
  }

  onDestroy(): void {
    VaLog.info(TAG, `CallAssistantCallLogAbility onDestroy`);
  }

  onSessionCreate(want: Want, session: UIExtensionContentSession): void {
    let storage: LocalStorage = new LocalStorage();

    VaLog.info(TAG, `CallAssistantCallLogAbility onSessionCreate`)
  }

  onSessionDestroy(session: UIExtensionContentSession): void {
    VaLog.info(TAG, `CallAssistantCallLogAbility onSessionDestroy`)
  }

  loadCallAssistantPrivacyPage(session: UIExtensionContentSession, storage: LocalStorage) {
    GlobalPreloadUtil.preload(this.context);
    session.loadContent('pages/celiacall/CallAssistantCallLogPage', storage);
    try {
      session?.setWindowBackgroundColor('#00000000');
    } catch (e) {
      VaLog.error(TAG, 'setWindowBackgroundColor ' + SafeJson.ohAegJsonStringify(e));
    }
  }
}