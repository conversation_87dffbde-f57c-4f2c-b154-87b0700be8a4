
/**
 * 动画状态
 */
export enum AnimState {
  /**
   * initial animate
   */
  NONE_ANIM = 1000,

  /**
   * static animate
   */
  STATIC_ANIM = -1,

  /**
   * wait animate
   */
  WAIT_ANIM = 0,

  /**
   * wait to input animate
   */
  WAIT_TO_INPUT_ANIM = 1,

  /**
   * input animate
   */
  INPUT_ANIM = 2 ,

  /**
   * input to thinking animate
   */
  INPUT_TO_THINK_ANIM = 3,

  /**
   * thinking animate
   */
  THINK_ANIM = 4,

  /**
   * thinking to present(wait) animate
   */
  THINK_TO_PRESENT_AIM = 5,

  /**
   * present(wait) animate
   */
  PRESENT_ANIM = 6,

  INPUT_TO_WAIT_ANIM = 7
}

/**
 * 当前状态是否小于输入的状态
 *
 * @param other 输入的状态
 * @return true 当前状态小于输入状态 false 当前状态大于等于输入状态
 */
export function before(me: AnimState, other : AnimState): boolean {
  return me < other;
}

/**
 * 当前状态是否小于等于输入的状态
 *
 * @param other 输入的状态
 * @return true 当前状态小于等于输入状态 false 当前状态大于输入状态
 */
export function  beforeOrIs(me: AnimState, other : AnimState): boolean {
  return me <= other;
}