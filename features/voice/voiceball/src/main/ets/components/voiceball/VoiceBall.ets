import lazy { default as scene3d } from '@ohos.graphics.scene';
import { VoiceBall3D } from '@vassistant/common-voiceball3d/src/main/ets/components/voiceball/VoiceBall3D'
import { PageLifeCycle } from './PageLifeCycle';
import { VoiceBallState } from '@hms-assistant/common-corebase/src/main/ets/constant/Constants';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { BaseUtils } from '@hms-assistant/common-corebase/src/main/ets/util/BaseUtils';
import {
  STORAGE_KEY_FROM_HALF_PAGE,
  STORAGE_KEY_HALF_BALL_HIDE,
  STORAGE_KEY_ACCOUNT_LOGIN_STATE,
  STORAGE_KEY_IS_BALL_LISTENING,
  STORAGE_KEY_BALL_STATE,
  STORAGE_KEY_IS_DARK_MODE,
  STORAGE_KEY_ON_APP,
  STORAGE_KEY_HALF_CHANGE_FULL} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { AnimStateManager } from './AnimStateManager';
import { BallAnimViewDrawer } from '@vassistant/common-voiceball3d/src/main/ets/components/voiceball/BallAnimViewDrawer';
import lazy { default as displaySync } from '@ohos.graphics.displaySync';
import { FloatScreenMgt } from '@hms-assistant/common-corebase/src/main/ets/util/FloatScreenMgt';
import lazy { default as accessibility } from '@ohos.accessibility';
import lazy { SoundPlayUtils } from '@hms-assistant/common-corebase/src/main/ets/util/SoundPlayUtils';

const TAG: string = '[VoiceBall]';

/**
 * 组件-语音球
 *
 * <AUTHOR> @since 2022-09-29
 */
@Component
export struct VoiceBall {
  @StorageLink(STORAGE_KEY_IS_BALL_LISTENING) @Watch('onChanged') isBallListening: boolean = false;
  @StorageLink(STORAGE_KEY_ACCOUNT_LOGIN_STATE) @Watch('accountOnChange') isHuaweiAccountLogin: boolean = true;
  @StorageLink(STORAGE_KEY_BALL_STATE) @Watch('onBallStateChange') ballState: number = VoiceBallState.NONE;
  @StorageLink('SOUND_LEVEL') @Watch('onSoundLevelChange') soundLevel: number = 0;
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) @Watch('onDarkModeChange') isDarkMode: boolean = false;
  @Prop innerOffset: number = 17;
  @Prop @Watch('onBackgroundDark') isBackgroundDark: boolean = false;
  @Prop onlyRunIdleAnim: boolean = false;
  @Prop enableDfaultShake: boolean = false;
  @Prop enableInAnim: boolean = false;
  @Prop fromPage: string = ''
  @Prop stopWhenIdle: boolean = true;
  @Prop idlePatelSpeed: number = 0.8;
  @Prop enableCanvasInAnim: boolean = false;
  @Prop canvasInAnimDurationOne: number = 660;
  @Prop canvasInAnimDurationTwo: number = 500;
  @Prop canvasMoveDownDelayTime: number = 500;
  @Prop noIdleComponentSize: number = 1
  @Prop idleComponentSize: number = 0.9
  @Prop circleColorGammaLight: number = 1.2;
  @Prop isFocusable: boolean = true;

  isSupportBall: boolean = scene3d !== undefined;
  onVoiceBallClick?: (listening: boolean) => void;
  pageLifeCycle?: PageLifeCycle;
  isInWakeupRecord?: boolean;
  @Prop ballSize: number = 96;
  @Prop listenBarScale: number = 1;
  @State frameAnimStatus: AnimationStatus = AnimationStatus.Initial;
  @Prop @Watch('onLongPressChange') isLongPress:boolean = false;
  @Prop @Watch('onAnimPauseChange') animPause: boolean = false;
  private isBallShow: boolean = true;
  private lastBallState = VoiceBallState.NONE;
  private voiceStateManager: AnimStateManager = new AnimStateManager();
  private animView: BallAnimViewDrawer = new BallAnimViewDrawer();
  private sync60: ExpectedFrameRateRange = { expected: 50, min: 0, max: 120 };
  private sync1: ExpectedFrameRateRange = { expected: 1, min: 0, max: 30 };
  private backDisplaySync?: displaySync.DisplaySync = undefined;
  private isOnApp: boolean = false;
  private timeId = 0;

  aboutToAppear() {
    Logger.info(TAG, `aboutToAppear fromPage:${this.fromPage}, animPause: ${this.animPause}`);
    if (this.pageLifeCycle) {
      this.pageLifeCycle.onPageShown = this.onBallShow;
      this.pageLifeCycle.onPageHidden = this.onBallHide;
    }
    this.updateFrameAnim();
    this.voiceStateManager.setBallAnimViewDrawer(this.animView)
    this.startFrameSync();
    this.resetHalfPageState();
    let onAppNew = AppStorage.get<boolean>(STORAGE_KEY_ON_APP);
    if (onAppNew !== undefined) {
      this.isOnApp = onAppNew;
    }

    // 刚进入就有状态，也要响应
    if (this.ballState !== VoiceBallState.NONE) {
      this.onBallStateChange();
    }

    if (accessibility.isOpenTouchGuideSync()) {
      SoundPlayUtils.init();
    }
  }

  onBackgroundDark() {
    Logger.info(TAG, `onBackgroundDark: ${this.isBackgroundDark}`);
  }

  onLongPressChange() {
    this.animView.updateLongPress(this.isLongPress)
  }

  onDarkModeChange() {
    this.animView.updateDarkMode(this.isDarkMode);
  }

  private startFrameSync() {
    if (this.fromPage === 'PcChat') {
      this.startFrameSyncPC();
    } else {
      this.startFrameSyncNormal();
    }
  }

  private startFrameSyncNormal() {
    this.backDisplaySync?.stop();
    this.backDisplaySync = undefined;
    if (this.animPause) {
      setTimeout(() => {
        this.voiceStateManager.onDrawFrame();
      }, 500)
      return;
    }
    if (!this.backDisplaySync) {
      this.backDisplaySync = displaySync.create()
      this.backDisplaySync.setExpectedFrameRateRange(this.sync60);
      this.backDisplaySync.on('frame', frameInfo => {
        this.doFrame();
      });
      this.backDisplaySync.start();
    }
  }

  private doFrame() {
    // 半屏只要进入退出状态，就不刷新了，不然底层容易crash
    if (this.isHalfPageHide()) {
      return;
    }
    this.voiceStateManager.onDrawFrame();
    let onAppNew = AppStorage.get<boolean>(STORAGE_KEY_ON_APP);
    if (onAppNew !== undefined && onAppNew !== this.isOnApp) {
      this.isOnApp = onAppNew
      this.animView.updateOnApp(this.isOnApp);
    }
  }

  private startFrameSyncPC() {
    if (this.timeId > 0) {
      clearInterval(this.timeId)
      this.timeId = 0;
    }
    if (this.animPause) {
      setTimeout(() => {
        this.voiceStateManager.onDrawFrame();
      }, 500)
      return;
    }
    this.timeId = setInterval(() => {
      this.doFrame();
    }, 20)
  }

  onAnimPauseChange() {
    Logger.info(TAG, `onAnimPauseChange ${this.animPause}`);
    this.startFrameSync()
  }

  @Builder
  static viewBuilderCapsuleBar(fromPage: string, disableAllAnim: boolean = false, ballSize: number = 96,
    pageLifeCycle?: PageLifeCycle, translate?: TranslateOptions, scale?: ScaleOptions) {
    VoiceBall({
      fromPage: fromPage,
      ballSize: ballSize,
      onlyRunIdleAnim: disableAllAnim,
      pageLifeCycle: pageLifeCycle
    })
      .translate(translate)
      .scale(scale)
  }

  private isHalfPageHide(): boolean {
    if (!AppStorage.get(STORAGE_KEY_HALF_BALL_HIDE)) {
      return false;
    }
    if (this.fromPage !== STORAGE_KEY_FROM_HALF_PAGE) {
      return false;
    }
    return true;
  }

  private resetHalfPageState() {
    if (this.fromPage === STORAGE_KEY_FROM_HALF_PAGE) {
      AppStorage.setOrCreate<boolean>(STORAGE_KEY_HALF_BALL_HIDE, false);
    }
  }

  onSoundLevelChange() {
    if (!this.isBallShow) {
      return;
    }
    this.voiceStateManager.soundLevelUpdated(this.soundLevel);
  }

  onBallStateChange() {
    if (!this.canReceiveStateChange()) {
      return;
    }
    Logger.info(TAG, `onBallStateChange: ${this.ballState}`);
    if (this.lastBallState === this.ballState) {
      Logger.info(TAG, `onBallStateChange: fromPage ${this.fromPage} ballState same ${this.ballState}`);
      return;
    }
    this.voiceStateManager.onStateChanged(this.lastBallState, this.ballState);
    this.lastBallState = this.ballState;
  }

  private canReceiveStateChange() {
    Logger.info(TAG, `canReceiveStateChange change page ${this.fromPage} full ${AppStorage.get(STORAGE_KEY_HALF_CHANGE_FULL)} isBallShow ${this.isBallShow}`);
    if (this.fromPage === STORAGE_KEY_FROM_HALF_PAGE) {
      return true;
    }
    if(AppStorage.get(STORAGE_KEY_HALF_CHANGE_FULL)) {
      return true;
    }
    if (!this.isBallShow) {
      return false;
    }
    return true;
  }

  aboutToDisappear() {
    Logger.info(TAG, `aboutToDisappear ${this.fromPage}`);
    this.frameAnimStatus = AnimationStatus.Stopped;
    this.backDisplaySync?.stop();
    this.backDisplaySync = undefined;
    if (this.timeId > 0) {
      clearInterval(this.timeId);
      this.timeId = 0;
    }
    SoundPlayUtils.unload();
  }

  onChanged(propName: string) {
    Logger.info(TAG, `onChanged: ${propName} -> ${this.isBallListening}`);
    if (accessibility.isOpenTouchGuideSync()) {
      if (this.isBallListening) {
        SoundPlayUtils.playSoundFromRawFd('talkback_start.ogg')
      } else {
        SoundPlayUtils.playSoundFromRawFd('talkback_stop.ogg')
      }
    }
  }

  private onBallShow = () => {
    Logger.info(TAG, `onBallShow ${this.fromPage}  ballState ${this.ballState}`);
    this.isBallShow = true;
    this.updateFrameAnim();

    // 刚进入就有状态，也要响应
    if (this.ballState !== VoiceBallState.NONE) {
      this.onBallStateChange();
    }
  }
  private onBallHide = () => {
    Logger.info(TAG, `onBallHide ${this.fromPage}`);
    this.ballState = VoiceBallState.NONE;
    this.lastBallState = VoiceBallState.NONE;
    this.voiceStateManager.reset()
    this.isBallShow = false;
    this.updateFrameAnim();
  }

  private updateFrameAnim(): void {
    if (this.isInWakeupRecord) {
      if (this.isBallShow) {
        this.frameAnimStatus = AnimationStatus.Running;
      } else {
        this.frameAnimStatus = AnimationStatus.Paused;
      }
      return;
    }

    if (this.isHuaweiAccountLogin) {
      if (this.isBallShow) {
        this.frameAnimStatus = AnimationStatus.Running;
      } else {
        this.frameAnimStatus = AnimationStatus.Paused;
      }
    } else {
      this.frameAnimStatus = AnimationStatus.Initial;
    }
  }

  accountOnChange() {
    Logger.info(TAG, 'account change.');
    this.updateFrameAnim();
  }

  @Builder
  build3D() {
    Stack({ alignContent: Alignment.Center }) {
      VoiceBall3D({
        fromPage: this.fromPage,
        ballAnimViewImpl: this.animView,
        enableDfaultShake: this.isInWakeupRecord || this.enableDfaultShake,
        isNightMode: this.isDarkMode,
        isBackgroundDark: this.isBackgroundDark,
        enableInAnim: this.enableInAnim,
        ballSize: this.ballSize,
        listenBarScale: this.listenBarScale,
        onlyRunIdleAnim: this.onlyRunIdleAnim,
        stopWhenIdle: this.stopWhenIdle,
        idlePatelSpeed: this.idlePatelSpeed,
        enableCanvasInAnim: this.enableCanvasInAnim,
        canvasInAnimDurationOne: this.canvasInAnimDurationOne,
        canvasInAnimDurationTwo: this.canvasInAnimDurationTwo,
        canvasMoveDownDelayTime: this.canvasMoveDownDelayTime,
        noIdleComponentSize: this.noIdleComponentSize,
        idleComponentSize: this.idleComponentSize,
        circleColorGammaLight: this.circleColorGammaLight
      })
        .id('float_bar.voice_ball_3d')
        .width(this.ballSize)
        .height(this.ballSize)
        .onAppear(() => {
          this.animView.updateLongPress(this.isLongPress)
        })
    }
    .accessibilityGroup(true)
    .accessibilityText(this.isBallListening ? '停止收音' : '收音')
    .accessibilityDescription(this.isInWakeupRecord && this.isBallListening ? '不可用' : '')
    .hitTestBehavior(HitTestMode.Transparent)
    .draggable(false)
    .focusable(this.isFocusable)
    .onClick(() => {
      this.handleClick()
    });
  }

  handleClick() {
    if (BaseUtils.isFastClick()) {
      Logger.warn(TAG, 'ignore fast click');
      return;
    }
    if (this.isHuaweiAccountLogin === false) {
      Logger.info(TAG, 'voiceBall click without accountLogin or UID.');
      if (!FloatScreenMgt.getInstance().isVoiceRecordPhraseActive()) {
        return;
      }
    }
    if (this.onVoiceBallClick) {
      this.onVoiceBallClick(!this.isBallListening);
    }
  }

  build() {
    this.build3D();
  }
}