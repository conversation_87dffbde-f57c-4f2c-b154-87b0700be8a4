import { Deque, Queue } from '@kit.ArkTS'
import { VoiceBallState } from '@hms-assistant/common-corebase/src/main/ets/constant/Constants';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { BallAnimViewDrawer } from '@vassistant/common-voiceball3d/src/main/ets/components/voiceball/BallAnimViewDrawer';
import { AnimState, before, beforeOrIs } from './AnimState';
import { STORAGE_KEY_ON_APP } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import systemDateTime from '@ohos.systemDateTime';

const TAG = 'AnimStateManager';

/**
 * 只关注动画的状态变化
 */
export class AnimStateManager {
  private stateQueue: Deque<VoiceBallState> = new Deque();
  private currentState: VoiceBallState = VoiceBallState.NONE;
  private lastState: VoiceBallState = VoiceBallState.NONE;
  private animState: AnimState = AnimState.NONE_ANIM;
  /**
   * 过渡状态动画是否完成
   */
  private animatorStatesMap = new Map<number, boolean>();

  private ballAnimViewDrawer : BallAnimViewDrawer = new BallAnimViewDrawer();

  /**
   * 重置动画状态
   */
  public reset() {
    // 重置当前动画状态
    this.currentState = VoiceBallState.NONE;
    this.clearQueue();
  }

   /**
    * Notify voice state changed
    *
    * @param oldState old voice state
    * @param newState new voice state
    */
   public onStateChanged(oldState: VoiceBallState, newState: VoiceBallState) {
      Logger.info(TAG, 'oldState ' + oldState + ' newState ' + newState + ' this.animState ' + this.animState);
      if (newState === this.stateQueue.getFirst()) {
        Logger.info(TAG, 'state recycle, clear all old state');
         this.clearQueue();
      }
      this.stateQueue.insertEnd(newState);
     if (this.animState === AnimState.THINK_ANIM && newState === VoiceBallState.INPUTTING) {
       Logger.info(TAG, 'abortThinkAnim');
       this.ballAnimViewDrawer.abortThinkAnim();
       // 打断后立刻触发下刷新帧，立刻刷新状态
       this.onDrawFrame()
     }
   }

   /**
    * 用户输入音量值更新
    *
    * @param soundLevel Must be [0 - 100]
    */
   public soundLevelUpdated(soundLevel: number) {
     // 调用球的声强渲染
     this.ballAnimViewDrawer.soundLevelUpdated(soundLevel)
   }

   public onDrawFrame() {
      if (this.currentState === VoiceBallState.NONE) {
         this.pollState();
         this.changeAnimateState();
         this.draw()
      } else {
         let isTransitionAnimating = this.draw();
         if (!isTransitionAnimating) {
            this.pollState();
         }
         this.changeAnimateState();
      }
     this.ballAnimViewDrawer.onFrameBegin();
   }

   public setBallAnimViewDrawer(view : BallAnimViewDrawer) {
     this.ballAnimViewDrawer = view;
   }

   private draw() : boolean {
      let isTransitionAnimating = false;
      switch (this.animState) {
        case AnimState.NONE_ANIM: // 初始态，无动画
          break;
        case AnimState.STATIC_ANIM:
          isTransitionAnimating = this.drawStatic();
          break;
        case AnimState.WAIT_ANIM: // idle态入场及稳态动画，入场时不能切换支持立即切换到下一个状态
          isTransitionAnimating = this.drawIdle();
          break;
        case AnimState.WAIT_TO_INPUT_ANIM: // idle to input过渡动画，动画做完才支持切换下一状态
          isTransitionAnimating = this.drawIdleToListening();
          break;
        case AnimState.INPUT_ANIM: // input态稳态动画，支持立即切换到下一个状态
          this.drawListening();
          break;
        case AnimState.INPUT_TO_THINK_ANIM: // input to think过渡动画，动画做完才支持切换下一状态
          isTransitionAnimating = this.drawListeningToThinking();
          break;
        case AnimState.THINK_ANIM: // think态稳态动画，支持立即切换到下一个状态
          isTransitionAnimating = this.drawThinking(this.lastState === VoiceBallState.INPUTTING);
          break;
        case AnimState.THINK_TO_PRESENT_AIM: // input to idle过渡动画，动画做完才支持切换下一状态
          isTransitionAnimating = this.drawThinkingToPresent();
          break;
        case AnimState.PRESENT_ANIM:
          let onAppNew = AppStorage.get<boolean>(STORAGE_KEY_ON_APP);
          if (onAppNew) {
            isTransitionAnimating = this.drawOnAppFly();
          } else {
            isTransitionAnimating = this.drawHandling();
          }
          break;
        case AnimState.INPUT_TO_WAIT_ANIM:
          isTransitionAnimating = this.drawListeningToWait();
          break;
        default:
          break;
      }

      return isTransitionAnimating;
   }

   private drawStatic() {
      let isAnimate = this.ballAnimViewDrawer.drawStatic();
      return isAnimate;
   }

   private drawIdle() {
     let isAnimate = this.ballAnimViewDrawer.drawIdle();
      return isAnimate;
   }

   private drawIdleToListening() {
     let isAnimate = this.ballAnimViewDrawer.drawIdleToListening();
     if (!isAnimate) {
       this.animatorStatesMap.set(AnimState.WAIT_TO_INPUT_ANIM, true);
     }
     return isAnimate;
   }


   private drawListening() {
     let isAnimate = this.ballAnimViewDrawer.drawListening();
      return isAnimate;
   }

   private drawListeningToThinking() {
     let isAnimate = this.ballAnimViewDrawer.drawListeningToThinking();
     if (!isAnimate) {
       this.animatorStatesMap.set(AnimState.INPUT_TO_THINK_ANIM, true);
     }
     return true;
   }

  private drawListeningToWait() {
    let isAnimate = this.ballAnimViewDrawer.drawListeningToIdle();
    if (!isAnimate) {
      this.animatorStatesMap.set(AnimState.INPUT_TO_WAIT_ANIM, true);
    }
    return isAnimate;
  }


  private drawThinking(fromListen: boolean = true) {
     let isAnimate = this.ballAnimViewDrawer.drawThinking(fromListen);
      return isAnimate;
   }

   private drawThinkingToPresent() {
     let isAnimate = this.ballAnimViewDrawer.drawThinkingToPresent();
      return isAnimate;
   }

  private drawHandling() {
    return this.ballAnimViewDrawer.drawHandle();
  }

  private drawOnAppFly() {
    return this.ballAnimViewDrawer.drawOnAppFly();
  }

   private pollState() {
    if (this.stateQueue.length > 0) {
      this.lastState = this.currentState;
      this.currentState = this.stateQueue.popFirst();
      Logger.info(TAG, `poll state : ${this.currentState} currentAnim ${this.animState} this.lastState ${this.lastState}`);
    }
  }

  private changeAnimateState() {
    if (this.currentState === null) {
      Logger.warn(TAG, 'state null');
      return;
    }
    switch (this.currentState) {
      case VoiceBallState.WAITING:
        this.changeWaiting();
        break;
      case VoiceBallState.INPUTTING:
        this.changeInputting();
        break;
      case VoiceBallState.THINKING:
        this.changeThinking();
        break;
      case VoiceBallState.HANDLING:
        this.changePresent();
        break;
      default:
        break;
    }
  }

  private changeWaiting() {
    if (this.animState === AnimState.INPUT_TO_WAIT_ANIM) {
      if (this.animatorStatesMap.get(AnimState.INPUT_TO_WAIT_ANIM)) {
        this.animatorStatesMap.set(AnimState.INPUT_TO_WAIT_ANIM, false);
        this.animState = AnimState.WAIT_ANIM;
      }
      return;
    }
    if (this.animState === AnimState.INPUT_ANIM || this.animState === AnimState.WAIT_TO_INPUT_ANIM) {
      this.animatorStatesMap.set(AnimState.INPUT_TO_WAIT_ANIM, false);
      this.animState = AnimState.INPUT_TO_WAIT_ANIM;
      return;
    }
    this.animState = AnimState.WAIT_ANIM;

  }

  private changeInputting() {
    if (this.animState === AnimState.WAIT_TO_INPUT_ANIM) {
      if (this.animatorStatesMap.get(AnimState.WAIT_TO_INPUT_ANIM)) {
        this.animatorStatesMap.set(AnimState.WAIT_TO_INPUT_ANIM, false);
        this.animState = AnimState.INPUT_ANIM;
      }
      return;
    }
    if (before(this.animState, AnimState.WAIT_TO_INPUT_ANIM) || this.animState === AnimState.PRESENT_ANIM ||
      this.animState === AnimState.INPUT_TO_WAIT_ANIM || this.animState === AnimState.THINK_ANIM) {
      this.animatorStatesMap.set(AnimState.WAIT_TO_INPUT_ANIM, false);
      this.animState = AnimState.WAIT_TO_INPUT_ANIM;
      return;
    }
    if (this.animState !== AnimState.INPUT_ANIM) {
      this.animState = AnimState.INPUT_ANIM;
    }
  }

  private changeThinking() {
    if (this.animState === AnimState.INPUT_TO_THINK_ANIM) {
      if (this.animatorStatesMap.get(AnimState.INPUT_TO_THINK_ANIM)) {
        this.animatorStatesMap.set(AnimState.INPUT_TO_THINK_ANIM, false);
        this.animState = AnimState.THINK_ANIM;
      }
      return;
    }
    if (before(this.animState, AnimState.INPUT_TO_THINK_ANIM) && this.animState !== AnimState.WAIT_ANIM) {
      this.animatorStatesMap.set(AnimState.INPUT_TO_THINK_ANIM, false);
      this.animState = AnimState.INPUT_TO_THINK_ANIM;
      return;
    }
    if (this.animState !== AnimState.THINK_ANIM) {
      this.animState = AnimState.THINK_ANIM;
    }
  }

  private changePresent() {
    if (this.animState !== AnimState.PRESENT_ANIM) {
      this.animState = AnimState.PRESENT_ANIM;
    }
  }

  private clearQueue() {
    Logger.info(TAG, 'clearQueue');
    while (this.stateQueue.length > 0) {
      this.stateQueue.popFirst();
    }
  }
}