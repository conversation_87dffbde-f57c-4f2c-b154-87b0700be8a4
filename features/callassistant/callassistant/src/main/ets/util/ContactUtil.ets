/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { CallLog } from '../bean/CallLog';
import DefaultCallData from '../bean/DefaultCallData';
import { CommonUtil } from './CommonUtil';
import call from '@ohos.telephony.call';
import { isDebugOn, VaLog } from './VaLog';
import { dataShare, dataSharePredicates, DataShareResultSet } from '@kit.ArkData';
import { image } from '@kit.ImageKit';
import { BusinessError, osAccount, settings, systemParameterEnhance } from '@kit.BasicServicesKit';
import {
  abilityAccessCtrl,
  bundleManager,
  common,
  Context,
  UIExtensionContentSession,
  uriPermissionManager,
  wantConstant,
  contextConstant
} from '@kit.AbilityKit';
import {
  AUDIO_INFO,
  CALL_DATA,
  CALL_DATA_SOURCE,
  CallAssistantConstants,
  FIRST_ENTER,
  INPUT_ENABLE,
  TODO_ADDED_TAG,
  UI_SESSION_KEY,
  MARKTYPE_STRANGER,
  SaveNotepadStatus,
  TIME_OUT,
  STORAGE_KEY_IS_UI_EXTENSION
} from '../constants/CallAssistantConstants';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import CallRecord from '../bean/CallRecord';
import { CallAssistantTextBean } from '../bean/CallAssistantTextBean';
import Calls from '../bean/Calls';
import { intl } from '@kit.LocalizationKit';
import IntentsApi, { ContactInfo, NotePadSummaryParams, ScheduleInfo } from '../logic/IntentsApi';
import { ChatDataSource } from '../pages/mainpage/ChatDataSource';
import AudioInfo from '../bean/AudioInfo';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils'
import { contact } from '@kit.ContactsKit';
import process from '@ohos.process';
import { TimeUtil } from './TimeUtil'
import { fileUri } from '@kit.CoreFileKit';
import { textProcessing } from '@kit.NaturalLanguageKit';
import { EntityType } from '@hms.ai.nlp.textProcessing';

const DB_BASE_URI = 'datashare:///com.ohos.contactsdataability';
const DB_URI = DB_BASE_URI + '/contacts/contact_data';
const RAW_DB_URI = DB_BASE_URI + '/contacts/raw_contact';
const RAW_CONTACT_ID = 'raw_contact_id';
const BLOB_DATA = 'blob_data';
const TYPE_ID = 'type_id';
const DATE_FOR_HOUR_TWELVE = new intl.DateTimeFormat('', { timeStyle: 'short', hourCycle: 'h12' });
const DATE_FOR_HOUR_TWENTY = new intl.DateTimeFormat('', { timeStyle: 'short', hourCycle: 'h24' });

let timeFormat: string | undefined = undefined;
const TAG = 'ContactUtil';

export namespace ContactUtil {
  export function getNameText(item?: CallLog): string {
    if (!item) {
      return '';
    }
    if (!CommonUtil.isEmptyStr(item.displayName)) {
      return item.displayName;
    } else if (!CommonUtil.isEmptyStr(item.formattedNumber)) {
      return item.formattedNumber;
    } else {
      return item.phoneNumber;
    }
  }

  export function isInPrivacyDomain(): boolean {
    let accountManager: osAccount.AccountManager = osAccount.getAccountManager();
    try {
      let localId: number = accountManager.getOsAccountLocalIdForUidSync(process.uid);
      VaLog.info(TAG, `is in privacy domian: ${localId}`)
      if (localId !== 100) {
        VaLog.info(TAG, `is In privacy domain, reject voice wakeup switch`);
        return true;
      }
    } catch (err) {
      VaLog.info(TAG, 'getOsAccountLocalIdForUidSync exception: ' + JSON.stringify(err));
    }
    return false;
  }

  export function getLocationText(item?: CallLog): string {
    if (!item) {
      return '';
    }
    return (item.numberLocation && item.numberLocation !== 'N') ?
      item.numberLocation as string : item.phoneNumber as string;
  }

  export function getCallLabelPrimary(callData: DefaultCallData, isBanner: boolean = true): Resource | string {
    const markContent = getMarkContentResource(callData);
    if (callData.isEcc) {
      if (markContent) {
        return markContent;
      }
      return $r('app.string.emergency');
    }
    if (callData.isVoiceMailNumber) {
      return $r('app.string.voicemail');
    }
    if (callData.contactName) {
      return callData.contactName;
    }
    if (markContent) {
      return markContent;
    }
    const phoneNumber = callData.formatNumber || callData.accountNumber;
    if (phoneNumber) {
      return phoneNumber;
    }
    return $r('app.string.unknownNumber');
  }

  export function getMarkContentResource(callData: DefaultCallData): Resource | string {
    if (!isNumberMarked(callData)) {
      return '';
    }
    let markTypeCase = callData.numberMarkInfo.markType.valueOf();
    let markType = call.MarkType;
    switch (markTypeCase) {
      case markType.MARK_TYPE_NONE:
        return '';
      case markType.MARK_TYPE_CRANK:
        return $r('app.string.harassing_call');
      case markType.MARK_TYPE_FRAUD:
        return $r('app.string.highrisk_phone_call');
      case markType.MARK_TYPE_EXPRESS:
        return $r('app.string.courier_and_food_delivery');
      case markType.MARK_TYPE_PROMOTE_SALES:
        return $r('app.string.advertising_and_promotion');
      case markType.MARK_TYPE_HOUSE_AGENT:
        return $r('app.string.Real_Estate_Agency');
      case markType.MARK_TYPE_INSURANCE:
        return $r('app.string.financial_management_insurance');
      case markType.MARK_TYPE_TAXI:
        return $r('app.string.taxi');
      default:
        return callData.numberMarkInfo.markContent;
    }
  }

  export function getMarkContent(callData?: DefaultCallData): string {
    if (callData === undefined) {
      return '';
    }
    if (!isNumberMarked(callData)) {
      return '';
    }
    if (callData.contactId) {
      if (callData.contactId <= 0) {
        return '陌生号码';
      }
    }
    let markTypeCase = callData.numberMarkInfo.markType.valueOf();
    let markType = call.MarkType;
    switch (markTypeCase) {
      case markType.MARK_TYPE_NONE:
        return '';
      case markType.MARK_TYPE_CRANK:
        return '骚扰电话';
      case markType.MARK_TYPE_FRAUD:
        return '高风险电话';
      case markType.MARK_TYPE_EXPRESS:
        return '快递送餐';
      case markType.MARK_TYPE_PROMOTE_SALES:
        return '广告推销';
      case markType.MARK_TYPE_HOUSE_AGENT:
        return '房产中介';
      case markType.MARK_TYPE_INSURANCE:
        return '';
      case markType.MARK_TYPE_TAXI:
        return '出租车';
      default:
        return callData.numberMarkInfo.markContent;
    }
  }

  export function getMarkContentFormIndex(inputType?: number): string {
    let markType = call.MarkType;
    switch (inputType) {
      case markType.MARK_TYPE_NONE:
        return '';
      case markType.MARK_TYPE_CRANK:
        return ResourceUtils.getResourceStringSync($r('app.string.harassing_call'));
      case markType.MARK_TYPE_FRAUD:
        return ResourceUtils.getResourceStringSync($r('app.string.highrisk_phone_call'));
      case markType.MARK_TYPE_EXPRESS:
        return ResourceUtils.getResourceStringSync($r('app.string.courier_and_food_delivery'));
      case markType.MARK_TYPE_PROMOTE_SALES:
        return ResourceUtils.getResourceStringSync($r('app.string.advertising_and_promotion'));
      case markType.MARK_TYPE_HOUSE_AGENT:
        return ResourceUtils.getResourceStringSync($r('app.string.Real_Estate_Agency'));
      case markType.MARK_TYPE_INSURANCE:
        return ResourceUtils.getResourceStringSync($r('app.string.financial_management_insurance'));
      case markType.MARK_TYPE_TAXI:
        return ResourceUtils.getResourceStringSync($r('app.string.taxi'));
      case MARKTYPE_STRANGER:
        return ResourceUtils.getResourceStringSync($r('app.string.stranger_phone_call'))
      default:
        return '';
    }
  }

  export function getContactNameFromKey(context: Context, key: string): Promise<string> {
    VaLog.info(TAG, 'key' + key);
    return new Promise((resolve, reject) => {
      contact.queryContact(context, key, (err: BusinessError, data) => {
        if (err) {
          VaLog.error(TAG, `Failed to query Contact. Code: ${err.code}, message: ${err.message}`);
          reject(''); // 拒绝Promise
        } else {
          VaLog.info(TAG, 'contactData' + SafeJson.ohAegJsonStringifyArray(data));
          if (data) {
            resolve(data.name?.fullName as string); // 解决Promise
          } else {
            resolve(''); // 如果没有找到姓名，返回''
          }
        }
      });
    });
  }

  export function isNumberMarked(callData: DefaultCallData): boolean {
    return !callData.numberMarkInfo ? false : !(callData.numberMarkInfo.markType?.valueOf() === undefined ||
      callData.numberMarkInfo.markType?.valueOf() === 0)
  }

  export async function getContactAvatar(context: Context, callData: DefaultCallData): Promise<PixelMap | undefined> {
    let dataAbilityHelper = await dataShare.createDataShareHelper(context, DB_BASE_URI);
    const PHOTO_COLUM = [RAW_CONTACT_ID, BLOB_DATA];
    let photoPredicate = new dataSharePredicates.DataSharePredicates();
    photoPredicate.equalTo(TYPE_ID, '8'); // type 8 means query photo
    photoPredicate.equalTo(RAW_CONTACT_ID, callData.contactId); // We need this to match.
    try {
      // Query photo first
      let avatarResSet = await dataAbilityHelper.query(DB_URI, photoPredicate, PHOTO_COLUM);
      if (avatarResSet.rowCount > 0) {
        avatarResSet.goToFirstRow();
        const blobData = avatarResSet.getBlob(avatarResSet.getColumnIndex(BLOB_DATA));
        // Convert to pixel map
        let imageSource = image.createImageSource(blobData.buffer);
        if (imageSource !== undefined) {
          let avatarWidth = vp2px(context.resourceManager.getNumber($r('app.float.vp_32')));
          let pixelMap = await imageSource.createPixelMap({
            desiredSize: {
              height: avatarWidth,
              width: avatarWidth
            }
          });
          VaLog.info(TAG, 'Update contact avatar successfully');
          imageSource.release().catch((error: BusinessError) => {
            VaLog.error(TAG, 'release imageSource error: ' + JSON.stringify(error.message));
          })
          return pixelMap;
        }
      }
    } catch (err) {
      VaLog.info(TAG, 'get avatar err')
      return undefined;
    }
    VaLog.info(TAG, 'no avatar')
    return undefined;
  }

  export function getStringByName(context: Context, name: string): string {
    try {
      return context.resourceManager.getStringByNameSync(name);
    } catch (e) {
      VaLog.error(TAG, 'getStringByName error:', name);
      return '';
    }
  }

  export function parseMarkdownSummary(record: Record<string, string | string[]>): string {
    let stringList: string[] = [];
    stringList.push(`**${record.title}**`)
    let keywordArray = record.keyWordArray as string[];
    keywordArray?.forEach((keywordItem) => {
      if (keywordItem !== undefined && keywordItem.length >= 3) {
        stringList.push(`- ${keywordItem.substring(2)}`)
      }
    })

    let todoItems = record.todoItems as string[];
    todoItems?.forEach((todoItem) => {
      if (todoItem !== undefined && todoItem.length >= 3) {
        stringList.push(`![TodoCard](${todoItem.substring(2)})`)
      }
    })

    return stringList.join('\n');
  }

  export function parseSummaryResult(generateText: string): Record<string, string | string[]> {
    if (generateText.match(/<title>([\s\S]*?)<\/title>/i)) {
      return parseSummaryResultNew(generateText)
    }
    VaLog.info(TAG, 'generateText:', generateText);
    let value: Record<string, string | string[]> = {};
    let strings = generateText.split('\n');
    let summaryItem = strings.find((item) => {
      return item.startsWith('摘要：') && item.length > 3;
    });
    if (summaryItem) {
      value.summary = summaryItem.substring(3);
    }

    let titleItem = strings.find((item) => {
      return item.startsWith('标题：') && item.length > 3;
    });
    if (titleItem) {
      value.title = titleItem.substring(3);
    }

    let todoItems = strings.find((item) => {
      return item.startsWith('待办：') && item.length > 3;
    });
    if (todoItems) {
      let splitTodos = todoItems.substring(3).split('；');
      let todoList: string[] = [];
      splitTodos?.forEach((todoItem) => {
        if (!CommonUtil.isEmptyStr(todoItem) && todoItem !== '无' && todoItem.length >= 3) {
          todoList?.push(todoItem.substring(2))
        }
      })
      value.todoItems = todoList;
    }

    let keyWordItems = strings.find((item) => {
      return item.startsWith('关键信息：') && item.length > 5;
    });
    if (keyWordItems) {
      let keyWordArray = keyWordItems.substring(5).split('；')
        .filter((item) => {
          return !CommonUtil.isEmptyStr(item) && item !== '无';
        });
      value.keyWordArray = keyWordArray;

      let keyWord = '';
      for (let i = 0; i < keyWordArray.length; i++) {
        keyWord += `${keyWordArray[i].trim()}`;
        if (i !== keyWordArray.length - 1) {
          keyWord += '；\n';
        }
      }
      value.keyWord = keyWord;
    }

    let addList: string[] = [];
    strings.forEach((item) => {
      if (item.startsWith(TODO_ADDED_TAG)) {
        addList.push(item.substring(8));
      }
    })
    value.addedList = addList;

    if (isDebugOn) {
      VaLog.info(TAG, 'parseSummaryResult:', SafeJson.ohAegJsonStringify(value));
    }
    return value;
  }

  export function parseSummaryResultNew(generateText: string): Record<string, string | string[]> {
    VaLog.info(TAG, 'generateText:', generateText);
    let value: Record<string, string | string[]> = {};
    let strings = generateText.split('\n');

    let titleMatch = generateText.match(/<title>([\s\S]*?)<\/title>/i)
    if (titleMatch) {
      value.title = titleMatch[1]
    }

    let summaryMatch = generateText.match(/<summary>([\s\S]*?)<\/summary>/i)
    if (summaryMatch) {
      value.summary = summaryMatch[1]
    }

    let keysMatch = generateText.match(/<keys>([\s\S]*?)<\/keys>/i)
    if (keysMatch) {
      let keyWordItems = keysMatch[1]
      let keyWordArray = keyWordItems.split('\n')
        .filter((item) => {
          return !CommonUtil.isEmptyStr(item) && item !== '无';
        });
      value.keyWordArray = keyWordArray;

      let keyWord = '';
      for (let i = 0; i < keyWordArray.length; i++) {
        keyWord += `${keyWordArray[i].trim()}`;
        if (i !== keyWordArray.length - 1) {
          keyWord += '；\n';
        }
      }
      value.keyWord = keyWord;
    }

    let tasksMatch = generateText.match(/<tasks>([\s\S]*?)<\/tasks>/i)
    if (tasksMatch) {
      let todoItems = tasksMatch[1]
      let splitTodos = todoItems.split('\n');
      let todoList: string[] = [];
      splitTodos?.forEach((todoItem) => {
        if (!CommonUtil.isEmptyStr(todoItem) && todoItem !== '无' && todoItem.length >= 3) {
          todoList?.push(todoItem.substring(2))
        }
      })
      value.todoItems = todoList;
    }

    let addList: string[] = [];
    strings.forEach((item) => {
      if (item.startsWith(TODO_ADDED_TAG)) {
        addList.push(item.substring(8));
      }
    })
    value.addedList = addList;

    if (isDebugOn) {
      VaLog.info(TAG, 'parseSummaryResult:', SafeJson.ohAegJsonStringify(value));
    }
    return value;
  }

  export async function createContentToNote(callRecord: CallRecord, callLog?: CallLog): Promise<(string | number)[]> {
    let content: Record<string, string>[] = [];
    let list: CallAssistantTextBean[] = SafeJson.ohAegJsonParse(callRecord.callRecord);
    let name = CommonUtil.isEmptyStr(callLog?.displayName) ? '对方' : (callLog?.displayName ?? '');
    let totalLength = 0;
    list?.forEach((item) => {
      if (CommonUtil.isSelfType(item.type) ||
        item.type === CallAssistantConstants.Record.TYPE_OTHER) {
        if (CommonUtil.isEmptyStr(item.text)) {
          return;
        }
        let manName: string = (CommonUtil.isSelfType(item.type)) ? '我' : name;
        let recordItem: Record<string, string> = {};
        recordItem.spoken_man = manName;
        totalLength += manName.length;
        recordItem.spoken_time = CommonUtil.createFormatTime(0, item.time);
        totalLength += recordItem.spoken_time?.length ?? 0;
        if (!CommonUtil.isEmptyStr(item.translateText)) {
          recordItem.spoken_content = item.text + '\n' + item.translateText;
          totalLength += (item.text?.length ?? 0 + item.translateText?.length ?? 0);
        } else {
          recordItem.spoken_content = item.text;
          totalLength += (item.text?.length ?? 0);
        }
        content.push(recordItem);
      }
    })
    return [SafeJson.ohAegJsonStringify(content), totalLength];
  }

  /**
   * 获取通话记录（备忘录标题）
   * @param callRecord 通话记录
   * @param summaryText 摘要
   * @param beginTime 开始时间
   * @param callLog 通话详情
   * @returns
   */
  export async function getRecord(callRecord: CallRecord, summaryText: string, beginTime: number,
    callLog?: CallLog): Promise<Record<string, string | number>> {
    let result: Record<string, string | number> = {};
    if (!CommonUtil.isEmptyStr(summaryText)) {
      let markdownSummary = ContactUtil.parseMarkdownSummary(ContactUtil.parseSummaryResult(summaryText));
      result.markdownSummary = markdownSummary;
    }
    let dateFormat = new Intl.DateTimeFormat('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    let title = ContactUtil.getNameText(callLog) + '_' + (dateFormat.format(beginTime * 1000)).replace(' ', '');
    result.title = title;
    let content = await ContactUtil.createContentToNote(callRecord, callLog);
    result.totalLength = content[1];
    result.content = content[0];
    return result;
  }

  /**
   * 获取通话记录（备忘录标题）
   * @param callRecord 通话记录
   * @param summaryText 摘要
   * @param beginTime 开始时间
   * @param callLog 通话详情
   * @returns
   */
  export async function getRecordWithParseResult(callRecord: CallRecord,
    parsedSummaryResult: Record<string, string | string[]>, beginTime: number,
    callLog?: CallLog): Promise<Record<string, string | number>> {
    let result: Record<string, string | number> = {};
    if (parsedSummaryResult != null || parsedSummaryResult != undefined) {
      let markdownSummary = ContactUtil.parseMarkdownSummary(parsedSummaryResult);
      result.markdownSummary = markdownSummary;
    }
    let dateFormat = new Intl.DateTimeFormat('zh-CN', {
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
    let title = ContactUtil.getNameText(callLog) + '_' + (dateFormat.format(beginTime * 1000)).replace(' ', '');
    result.title = title;
    let content = await ContactUtil.createContentToNote(callRecord, callLog);
    result.totalLength = content[1];
    result.content = content[0];
    return result;
  }

  /**
   * 保存至备忘录
   * @param context 上下文
   * @param callRecord 通话记录
   * @param summaryText 摘要
   * @param beginTime 开始时间
   * @param canRepeat 是否可重复
   * @param callLog 通话详情
   * @returns 写入结果
   */
  export async function saveToNotePad(context: common.UIAbilityContext | common.ServiceExtensionContext,
    record: Record<string, string | number>, beginTime: number, canRepeat: boolean): Promise<(string | number)[]> {
    try {
      await TimeUtil.startWithTimeout(IntentsApi.initIntentsKit(context), TIME_OUT);
    } catch (e) {
      // 超时再次触发一次（通话三分钟后仍无结果会刷新状态，不会进入死循环）
      await IntentsApi.initIntentsKit(context)
    }
    VaLog.info(TAG, `record:totalLength:${record.totalLength}`);
    let summaryParams =
      IntentsApi.createNoteSummaryParams(record.title as string, record.markdownSummary as string, beginTime,
        record.content as string, canRepeat);
    return dispatchToNotePad(summaryParams);
  }

  /**
   * 按照新的模式保存至备忘录
   * @param context 上下文
   * @param record 通话记录
   * @param parsedSummary 已解析过的摘要
   * @param beginTime 开始时间
   * @param callLog 通话详情
   * @returns 写入结果
   */
  export async function newSaveToNotePad(context: common.UIAbilityContext | common.ServiceExtensionContext,
    record: Record<string, string | number>, parsedSummary: Record<string, string | string[]>, beginTime: number,
    callLog?: CallLog): Promise<(string | number)[]> {
    await IntentsApi.initIntentsKit(context);
    let contactInfo = await GetContactInfo(callLog);
    let contactInfoArray: ContactInfo[] = [contactInfo];
    let contactInfoJson = SafeJson.ohAegJsonStringify(contactInfoArray);
    context.area = contextConstant.AreaMode.EL5;
    let callUri: string = await fileUri.getUriFromPath(CommonUtil.getAudioPath(context) + '/' + beginTime + '_ori.wav');
    context.area = contextConstant.AreaMode.EL2;
    await uriPermissionManager.grantUriPermission(callUri, wantConstant.Flags.FLAG_AUTH_READ_URI_PERMISSION,
      'com.huawei.hmos.notepad');
    // 提取日程信息
    let scheduleJson = await extractScheduleFromSummary(parsedSummary);
    VaLog.info(TAG, `extracted schedule: ${scheduleJson}`);
    // 处理markdown摘要内容，去除TodoCard
    let processedSummaryContent = removeTodoCardFromMarkdown(record.markdownSummary as string);
    VaLog.info(TAG, `processed summary content length: ${processedSummaryContent.length}`);
    let recordParams =
      IntentsApi.createNewNoteSummaryParams(record, parsedSummary, callUri, contactInfoJson, scheduleJson,
        beginTime.toString(), processedSummaryContent);
    return dispatchToNotePad(recordParams);
  }

  async function dispatchToNotePad(summaryParams: NotePadSummaryParams): Promise<(string | number)[]> {
    let disJson = IntentsApi.createDispatchJson(summaryParams);
    let dispatchResult: string = await IntentsApi.dispatchMessage(disJson);
    VaLog.info(TAG, `get disjson is ${disJson}`);
    try {
      let code =
        (SafeJson.ohAegJsonParse(dispatchResult)?.content?.contentData?.[0]?.payload?.executeResult?.code as number);
      let noteId =
        (SafeJson.ohAegJsonParse(dispatchResult)
        ?.content?.contentData?.[0]?.payload?.executeResult?.result?.noteId as string) ??
          '';
      if (isDebugOn) {
        VaLog.info(TAG, 'dispatchResult:', SafeJson.ohAegJsonStringify(dispatchResult));
      } else {
        // Get totalLength from summaryParams.intentParam.record_transmit if it's a string, or use a default message
        let totalLengthInfo = typeof summaryParams.intentParam.record_transmit === 'string'
          ? summaryParams.intentParam.record_transmit.length
          : 'N/A';
        VaLog.info(TAG, `dispatchResult:code:${code},noteId:${noteId},totalLength:${totalLengthInfo}`);
      }
      return [code, noteId];
    } catch (e) {
      VaLog.error(TAG, 'parse noteId error');
      return [SaveNotepadStatus.FAILED, ''];
    }
  }


  export async function isNewIntelligentCall() {
    return systemParameterEnhance.getSync('const.call.intelligent_callui_enable', 'false') === 'true';
  }

  export async function GetContactInfo(callLog?: CallLog): Promise<ContactInfo> {
    return {
      contact_name: callLog?.displayName,
      contact_number: callLog?.phoneNumber,
      contact_address: callLog?.numberLocation
    }
  }

  /**
   * 处理markdown摘要内容，去除TodoCard格式
   * @param markdownContent markdown格式的摘要内容
   * @returns 去除TodoCard后的内容
   */
  function removeTodoCardFromMarkdown(markdownContent: string): string {
    if (CommonUtil.isEmptyStr(markdownContent)) {
      return '';
    }

    // 使用正则表达式匹配并移除 ![TodoCard](内容) 格式
    // 匹配模式：\n![TodoCard](任意内容)
    const todoCardRegex = /\n!\[TodoCard\]\([^)]*\)/g;
    let processedContent = markdownContent.replace(todoCardRegex, '');

    // 清理可能出现的多余空行
    processedContent = processedContent.replace(/\n\s*\n/g, '\n');

    VaLog.info(TAG,
      `removeTodoCardFromMarkdown: original length=${markdownContent.length}, processed length=${processedContent.length}`);

    return processedContent.trim();
  }

  /**
   * 从摘要文本中提取日程信息
   * @param parsedSummary 已解析的摘要结果
   * @returns ScheduleInfo数组的JSON字符串
   */
  async function extractScheduleFromSummary(parsedSummary: Record<string, string | string[]>): Promise<string> {
    let scheduleList: ScheduleInfo[] = [];

    let todoItems = parsedSummary.todoItems as string[];
    if (todoItems && todoItems.length > 0) {
      for (let item of todoItems) {
        if (!CommonUtil.isEmptyStr(item)) {
          try {
            let words = await textProcessing.getEntity(item, {
              entityTypes: [EntityType.DATETIME]
            });

            let scheduleInfo: ScheduleInfo = {
              schedule_content: item,
              schedule_start_time: undefined,
              schedule_end_time: undefined,
              schedule_address: undefined
            };

            if (words && words.length > 0) {
              for (let i = 0; i < words.length; i++) {
                let entry = words[i];
                let jsonResult = entry.jsonObject;
                let record: Record<string, string | number> = SafeJson.ohAegJsonParse(jsonResult);
                let startTime = record?.startTimestamp as (number | undefined);
                let endTime = record?.endTimestamp as (number | undefined);
                if (isDebugOn) {
                  VaLog.info(TAG, `extractSchedule startTime:${startTime},endTime:${endTime}`);
                }
                if (startTime !== undefined) {
                  scheduleInfo.schedule_start_time = startTime.toString();
                }
                if (endTime !== undefined) {
                  scheduleInfo.schedule_end_time = endTime.toString();
                }
                if (record.minSection === 'P') {
                  break;
                }
              }
            }
            // 如果结束时间为空，设置为与开始时间相同
            if (scheduleInfo.schedule_start_time && !scheduleInfo.schedule_end_time) {
              scheduleInfo.schedule_end_time = scheduleInfo.schedule_start_time;
              VaLog.info(TAG, `Set schedule_end_time same as schedule_start_time: ${scheduleInfo.schedule_start_time}`);
            }
            scheduleList.push(scheduleInfo);
          } catch (error) {
            VaLog.error(TAG, `extractSchedule error for item: ${item}, error: ${error}`);
            scheduleList.push({
              schedule_content: item,
              schedule_start_time: undefined,
              schedule_end_time: undefined,
              schedule_address: undefined
            });
          }
        }
      }
    }

    return SafeJson.ohAegJsonStringify(scheduleList);
  }

  export function formatTime(date: number, now: Date = new Date()): string | Resource {
    let result: string | Resource = '';
    // If the value is not a number, the value is not parsed.
    if (Number.isNaN(date)) {
      return '';
    }
    let timestamp = date * 1000;
    let callTime = new Date(timestamp);
    if (callTime.getTime() > now.getTime()) {
      result = callTime.getFullYear() + '/' + (callTime.getMonth() + 1) + '/' + callTime.getDate();
    } else if (callTime.getFullYear() === now.getFullYear()) {
      if (callTime.getMonth() === now.getMonth()) {
        // Same month of the same year
        let timeDiff = Number.parseInt(((now.getTime() - callTime.getTime()) / 60000).toString());
        let dayDiff = now.getDate() - callTime.getDate();
        let year = callTime.getFullYear();
        let mounth = callTime.getMonth() + 1;
        let day = callTime.getDate();
        let hour = callTime.getHours();
        let minutes = callTime.getMinutes() < 10 ? '0' + callTime.getMinutes() : callTime.getMinutes().toString();
        if (dayDiff === 0) {
          // 同天
          if (timeDiff === 0) {
            result = $r('app.string.just');
          } else if (timeDiff < 60) {
            result = $r('app.string.minutes_ago', timeDiff);
          } else {
            let timeDetail: Record<string, Resource | string | undefined> = {};
            timeDetail.time = getDayMessage(year, mounth, day, hour, minutes);
            result = timeDetail.time
          }
        } else if (dayDiff === 1) {
          result = '昨天';
        } else {
          result = (callTime.getMonth() + 1) + '/' + callTime.getDate(); // 'MM/dd'
        }
      } else {
        result = (callTime.getMonth() + 1) + '/' + callTime.getDate();
      }
    } else {
      // 'yyyy/MM/dd'
      result = callTime.getFullYear() + '/' + (callTime.getMonth() + 1) + '/' + callTime.getDate();
    }
    return result;
  }

  export function getDayMessage(year: number, mounth: number, day: number, hour: number, minutes: string,
    systime?: number) {
    if ((systime && systime === 12) || Number.parseInt(judgeSysTime()) === 12) {
      const formattedDate: string = DATE_FOR_HOUR_TWELVE.format(new Date(year, mounth, day, hour, parseInt(minutes)));
      return formattedDate;
    } else {
      const formattedDate: string = DATE_FOR_HOUR_TWENTY.format(new Date(year, mounth, day, hour, parseInt(minutes)));
      return formattedDate;
    }
  }

  export function judgeSysTime(context?: Context): string {
    if (timeFormat !== undefined) {
      return timeFormat;
    }
    try {
      timeFormat = settings.getValueSync(context, settings.date.TIME_FORMAT, '12');
      return timeFormat;
    } catch (err) {
      VaLog.error('judgeSysTime', `judgeSysTime err: ${err}`);
      return '12';
    }
  }


  export function initLocalStorage(localStorage: LocalStorage, want: Want, session?: UIExtensionContentSession): void {
    try {
      let callData = CommonUtil.getValue(want.parameters?.params, 'callData') as DefaultCallData;
      let answerType = CommonUtil.getValue(want.parameters?.params, 'answerType') as number;
      let startFromBackground = CommonUtil.getValue(want.parameters?.params, 'startFromBackground') as boolean;
      if (startFromBackground !== undefined) {
        localStorage.setOrCreate<boolean>('startFromBackground', startFromBackground);
      }
      let type = Number.parseInt('' + answerType);
      localStorage.setOrCreate<number>(FIRST_ENTER, type);
      VaLog.info(TAG,
        `answerType:${type},callData:${isDebugOn ? SafeJson.ohAegJsonStringify(callData) : callData?.startTime}`);
      localStorage.setOrCreate<UIExtensionContentSession>(UI_SESSION_KEY, session);
      localStorage.setOrCreate<DefaultCallData>(CALL_DATA, callData);
      localStorage.setOrCreate<boolean>(INPUT_ENABLE, false);
      localStorage.setOrCreate<ChatDataSource>(CALL_DATA_SOURCE, new ChatDataSource());
      localStorage.setOrCreate<AudioInfo>(AUDIO_INFO, new AudioInfo());
      if (localStorage?.get<boolean>(STORAGE_KEY_IS_UI_EXTENSION)) {
        session?.setReceiveDataCallback((data: Object) => {
          let callData = CommonUtil.getValue(data, 'callData') as DefaultCallData;
          localStorage.setOrCreate<DefaultCallData>(CALL_DATA, callData);
        })
      }
    } catch (e) {
      VaLog.error(TAG, 'onSessionCreate error ' + e);
    }
  }

  export async function getContacts(context: common.UIExtensionContext): Promise<string[]> {
    let info = await bundleManager.getApplicationInfo('com.huawei.hmos.vassistant', 0);
    let checkResult =
      await abilityAccessCtrl.createAtManager().checkAccessToken(info.accessTokenId, 'ohos.permission.READ_CONTACTS');
    VaLog.info(TAG, 'getContacts checkResult:' + checkResult)
    if (checkResult !== abilityAccessCtrl.GrantStatus.PERMISSION_GRANTED) {
      return [];
    }
    let hotWords: string[] = [];
    let helper = await dataShare.createDataShareHelper(context, DB_BASE_URI);
    let condition = new dataSharePredicates.DataSharePredicates();
    condition.limit(1000, 0);
    VaLog.info(TAG, `startquery`);
    let columns = [Calls.DISPLAY_NAME];
    let resultSet: DataShareResultSet = await helper?.query(RAW_DB_URI, condition, columns);
    let index: number | undefined;
    while (resultSet.goToNextRow()) {
      if (index === undefined) {
        index = resultSet.getColumnIndex(Calls.DISPLAY_NAME);
        VaLog.info(TAG, `index:${index}`)
      }
      let name = resultSet.getString(index);
      hotWords.push(name);
    }
    VaLog.info(TAG, `getContacts end size ${hotWords.length}`)
    return hotWords;
  }
}
