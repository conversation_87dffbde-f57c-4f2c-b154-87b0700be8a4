import { radius, TextPointDrawer } from './TextPointDrawer';
import { Logger } from '../utils/Logger';
import { ViewDisplaySync } from './ViewDisplaySync';
import { systemDateTime } from '@kit.BasicServicesKit';

const TAG = 'WaitingView';
const FRAME_FAST = 26;
const FRAME_SLOW = 33;
const SLEEP_TIME = 83;
const FRAME_FAST_ANIM_TIME = 260;
const FRAME_SLOW_ANIM_TIME = 330;

@Component
export struct WaitingView {
  private setting: RenderingContextSettings = new RenderingContextSettings(true)
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.setting); // 动效显示
  private loadingPoint = new TextPointDrawer();
  private isCircle = false;
  private speedFast = true;
  @Prop
  private pointSize: number = 20;
  private displaySync: ViewDisplaySync = new ViewDisplaySync(FRAME_FAST);
  private sleepStartTime = 0;

  showLoading() {
    let line = 0;
    let leftOffset = 0;
    let lineTop = 0;
    let textPointHeight = this.pointSize;
    let textPointWidth = this.pointSize;
    let textPoint = this.loadingPoint;
    textPoint.row = line;
    textPoint.column = 0;
    textPoint.progress = 0;
    textPoint.xLeft = leftOffset;
    textPoint.top = lineTop;
    textPoint.startPoint = 0;
    textPoint.height = textPointHeight;
    textPoint.width = textPointWidth;
    textPoint.type = 5;
    textPoint.animTime = FRAME_FAST_ANIM_TIME;
    leftOffset = leftOffset + textPointWidth + (textPointWidth - 6 * radius);
    textPoint.showCanvasWithProgress(this.context);
  }

  aboutToAppear(): void {
    Logger.info(TAG, `aboutToAppear aboutToAppear`);
    this.showLoading();
    this.displaySync.createAndStartSync(() => {
      if(systemDateTime.getTime() - this.sleepStartTime <= SLEEP_TIME) {
        return;
      }
      this.context.reset();
      if (this.isCircle) {
        this.doCircleInfinitelyAnim();
      } else {
        this.doDrawInAnim();
      }
    })
  }

  doCircleInfinitelyAnim() {
    const textPoint = this.loadingPoint;
    textPoint.showCanvasWithProgress(this.context);
    if (textPoint.firstShowTime === 0) {
      textPoint.firstShowTime = systemDateTime.getTime();
    }
    textPoint.progress =
      Math.max(0, Math.min((systemDateTime.getTime() - textPoint.firstShowTime) / textPoint.animTime, 1));
    if (textPoint.progress >= 0.99) {
      textPoint.progress = 0;
      textPoint.firstShowTime = 0;
      this.speedFast = !this.speedFast
      textPoint.animTime = this.speedFast ? FRAME_FAST_ANIM_TIME : FRAME_SLOW_ANIM_TIME;
      this.displaySync.updateExpectedFrame(this.speedFast ? FRAME_FAST : FRAME_SLOW);
      this.sleepStartTime = systemDateTime.getTime();
    }
  }

  aboutToDisappear(): void {
    Logger.info(TAG, `aboutToAppear aboutToDisappear`);
    this.displaySync.stopSync();
  }

  doDrawInAnim() {
    // 1 进入入参渲染
    const textPoint = this.loadingPoint;
    Logger.info(TAG, `doWaitCanvasAnim textPoint.progress ${textPoint.progress}  this.isCircle ${this.isCircle}`);
    textPoint.showCanvasWithProgress(this.context);
    if (textPoint.firstShowTime === 0) {
      textPoint.firstShowTime = systemDateTime.getTime();
    }
    textPoint.progress =
      Math.max(0, Math.min((systemDateTime.getTime() - textPoint.firstShowTime) / textPoint.animTime, 1));
    if (textPoint.progress >= 0.99) {
      this.isCircle = true;
      textPoint.type = 6;
      textPoint.progress = 0;
      textPoint.firstShowTime = 0;
      this.sleepStartTime = systemDateTime.getTime();
    }
  }

  build() {
    Canvas(this.context)
      .width(this.pointSize)
      .height(this.pointSize)
  }
}