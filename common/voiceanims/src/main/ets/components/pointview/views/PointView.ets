import { LineCalc, onLineChangeListener } from './LineCalc'
import { TextPointManager } from './TextPointManager'
import { Logger } from '../utils/Logger';
import { BASE_POINT_ANIM_TIME, TOTAL_SHOW_COUNT } from '../utils/ConstUtil';
import { ViewDisplaySync} from './ViewDisplaySync';
import  text  from '@ohos.graphics.text'

const TAG = 'PointView';
const CHAR_BREAK = 10
const DEFAULT_BREAK_HEIGHT = 16;
const DEFAULT_MIN_WIDTH = 60;
const PRE_SHOW_LINE_COUNT =2;

export interface LineMetricCallback {
  getLastLineMetric: (line:number)=> text.LineMetrics | null
  getLineCount: ()=> number
}

@Component
export struct PointView {
  // 是否增加文字热区
  isAddTextArea:boolean=false;
  // 当前文本
  @Prop @Watch('onTxtChange') txt: string = '';

  // 所有的文本
  @Prop @Watch('onAllTxtChange') allTxt: string;

  @Prop @Watch('onSpeedChange') textShowSpeed: number;

  // 是否已完成
  @Prop @Watch('onFinish') isFinish: boolean

  @Prop @Watch('onDisableAnim') disableAnim : boolean;

  @Link txtCalcHeight : number;

  @Prop oneLineHeight : number = 16;

  @Prop onePointHeight: number = 14;

  @Prop parentMaxWidth: number;

  @Prop oneLineTopOffset : number = 0;

  @Prop oneLineBottomOffset : number = 0;

  @Prop @Watch('onViewVisibleChange') viewVisible : boolean ;

  @Prop fontSize : string ;

  @State pointHeight : number = 0;

  @Prop areaBackground : string | ResourceColor = '#000000';

  @Prop paddingValue : Padding | Length = {
    left: 0, top: 0, right: 0, bottom: 0
  }

  @Prop borderValue : BorderOptions = {
    radius: {
      topRight: '0vp',
      bottomLeft: '0vp',
      bottomRight: '0vp'
    }
  }
  @State
  private canvasWidth : number = 0;
  @State
  private canvasHeight : number = 0;

  @Consume @Watch('onMkHeightWithOutLastChange')  markdownHeightWithoutLast:number

  private areaWidth = 0;
  private areaHeight = 0;
  onAnimFinish?: () => void;
  @BuilderParam innerComponent?: () => void;
  @BuilderParam innerWaitComponent?: () => void;
  private setting: RenderingContextSettings = new RenderingContextSettings(true)
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.setting); // 动效显示
  private lastLineStartPos: number = 0;
  private lineCalc: LineCalc = new LineCalc();
  private textPointManager: TextPointManager = new TextPointManager(this.context, this.lineCalc);

  private lineMetricCallback? : LineMetricCallback;

  /**
   * canvas当前哪一行
   */
  private nowCanvasLine = 0;

  @Prop highStop:boolean;

  @Prop @Watch('onNowShowOnChange') nowShowOn: boolean = true;
  private displaySync : ViewDisplaySync = new ViewDisplaySync(60);

  onFinish() {
    let checkFinish = this.checkFinish();
    Logger.info(TAG, `onFinish ${this.isFinish}, checkFinish is ${checkFinish}`);
    if (checkFinish) {
      this.doFinish();
    } else {
      Logger.info(TAG, `checkFinish is false`);
    }
  }

  checkFinish() : boolean {
    return this.txt === this.allTxt && this.isFinish;
  }

  doFinish() {
    Logger.info(TAG, `doFinish`);
    this.textPointManager.stopAndClear();
    this.onAnimFinish?.();
  }

  onNowShowOnChange() {
    Logger.debug(TAG, `onNowShowOnChange ${this.nowShowOn}`);
    this.textPointManager.setNowShowOn(this.nowShowOn)
    if (!this.nowShowOn) {
      this.textPointManager.clearAllCanvas()
    }
  }

  onViewVisibleChange() {
    Logger.debug(TAG, `onViewVisibleChange ${this.viewVisible}`);
    if (!this.viewVisible) {
      this.canvasWidth = DEFAULT_MIN_WIDTH
      this.txt = ''
      this.textPointManager.clearMaxWidth()
    }
  }

  onMkHeightWithOutLastChange() {
    Logger.debug(TAG, `onMkHeightWithOutLastChange ${this.onMkHeightWithOutLastChange}`);
    this.lineCalc.setMarkdownHeightWithoutLast(this.markdownHeightWithoutLast)
  }

  aboutToDisappear(): void {
    Logger.info(TAG, `aboutToDisappear`);
    this.displaySync.stopSync()
    this.textPointManager?.stopAndClear();
  }

  onDisableAnim() {
    Logger.info(TAG, `onDisableAnim ${this.disableAnim}`);
    if (this.disableAnim) {
      this.textPointManager.stopAndClear();
      this.canvasWidth = DEFAULT_MIN_WIDTH
      this.displaySync.stopSync();
    }
  }

  onSpeedChange() {
    Logger.info(TAG, `onSpeedChange ${this.textShowSpeed}`);
    this.doUpdateSpeed();
  }

  onAllTxtChange() {
    Logger.info(TAG, `onAllTxtChange`);
  }

  /**
   * 更新卡片高度，由于stack不设置高度会默认全屏，所以需要指定卡片高度
   * @param calcStr
   * @param areaWidth
   */
  private updatePointViewHeight(calcStr : string, areaWidth : number) {
    if(this.highStop){
      return;
    }
    if (!calcStr || calcStr?.trim().length === 0) {
      this.pointHeight = 0;
      return;
    }
    this.pointHeight = this.canvasHeight;
  }

  onTxtChange() {
    Logger.debug(TAG, `onTxtChange checkFinish ${this.checkFinish()} this.disableAnim ${this.disableAnim}`)
    if (this.checkFinish() || this.disableAnim) {
      this.doFinish();
    } else {
      this.textPointManager.updateLineText(this.allTxt, this.lastLineStartPos, this.txt.length, this.nowCanvasLine,
        this.fontSize);
      if (this.nowCanvasLine === 0 && !this.textPointManager.isThisLineHaveStarted(0)) {
        this.textPointManager.showPointAnim(0);
      }
    }
    this.updatePointViewHeight(this.txt, this.areaWidth - this.getPaddingStartEnd());
    if (this.lineMetricCallback) {
      let lineCount = this.lineMetricCallback.getLineCount();
      this.lineCalc.updateLineMetrics(this.lineMetricCallback.getLastLineMetric(lineCount - 1))
    }
  }

  private doUpdateSpeed() {
    if (this.textShowSpeed <= 25) {
      this.textPointManager.setPointAnimTime(BASE_POINT_ANIM_TIME * 0.3)
      this.textPointManager.setMaxTotalShowCount(TOTAL_SHOW_COUNT)
    } else if (this.textShowSpeed <= 35) {
      this.textPointManager.setPointAnimTime(BASE_POINT_ANIM_TIME * 0.4)
      this.textPointManager.setMaxTotalShowCount(TOTAL_SHOW_COUNT)
    } else if (this.textShowSpeed <= 50) {
      this.textPointManager.setPointAnimTime(BASE_POINT_ANIM_TIME * 0.6)
      this.textPointManager.setMaxTotalShowCount(TOTAL_SHOW_COUNT)
    } else if (this.textShowSpeed < 80) {
      this.textPointManager.setPointAnimTime(BASE_POINT_ANIM_TIME * 0.8)
      this.textPointManager.setMaxTotalShowCount(TOTAL_SHOW_COUNT)
    } else {
      this.textPointManager.setPointAnimTime(BASE_POINT_ANIM_TIME)
      this.textPointManager.setMaxTotalShowCount(TOTAL_SHOW_COUNT)
    }
  }

  private getPaddingStartEnd() {
    let paddingV = this.paddingValue as Padding;
    if (paddingV === undefined) {
      return 0;
    }
    return (paddingV.left as number) + (paddingV.right as number);
  }

  private getPaddingTopBottom() {
    let paddingV = this.paddingValue as Padding;
    if (paddingV === undefined) {
      return 0;
    }
    return (paddingV.top as number) + (paddingV.bottom as number);
  }

  private getPaddingBottom() {
    let paddingV = this.paddingValue as Padding;
    if (paddingV === undefined) {
      return 0;
    }
    return (paddingV.bottom as number);
  }

  aboutToReuse(params: Record<string, object>) {
    Logger.debug(TAG, `aboutToReuse ${this.viewVisible}`);
    if (!this.viewVisible) {
      this.canvasWidth = DEFAULT_MIN_WIDTH
      this.txt = ''
      this.textPointManager.clearMaxWidth()
    }
  }

  aboutToAppear() {
    this.doUpdateSpeed();
    this.lineCalc.init(this.fontSize);
    this.lineCalc.setOneLineVp(this.oneLineHeight);
    this.lineCalc.setOnePointHeight(this.onePointHeight)
    this.lineCalc.setOneLineTopOffset(this.oneLineTopOffset);
    this.textPointManager.setBottomOffset(this.oneLineBottomOffset);
    this.textPointManager.setLineCalc(this.lineCalc);
    if (this.lineMetricCallback) {
      this.textPointManager.setLineMetricCallback(this.lineMetricCallback)
    }
    let lineListener: onLineChangeListener = {
      onLineChange: (_oldLine: number, _newLine: number) => {
        if (this.disableAnim) {
          Logger.info(TAG, `onLineChange disableAnim`);
          return;
        }
        this.textPointManager.clearOldLineBefore(_newLine)
          this.nowCanvasLine = _newLine;
          this.lastLineStartPos = this.txt.length - 1
          // 定制：换行有两种情况，\n和正常换行，  \n的不能算一行的启动点
          if (this.txt.charCodeAt(this.lastLineStartPos) === CHAR_BREAK) {
            this.lastLineStartPos++;
          }
          this.textPointManager.showPointAnim(this.nowCanvasLine)
        this.textPointManager.updateLineText(this.allTxt, this.lastLineStartPos, this.txt.length, this.nowCanvasLine,
          this.fontSize);
          Logger.info(TAG, `onLineChange this.lastLineStartPos ${this.lastLineStartPos} `);
        this.textPointManager.setNowShowLine(this.nowCanvasLine);
      }
    };
    this.lineCalc.setListener(lineListener);
    if (!this.disableAnim) {
      this.displaySync.createAndStartSync(() => {
        this.textPointManager.onFrame()
      });
    }
  }

  build() {
    Stack() {
      Stack() {
        this.innerWaitComponent?.();
      }.visibility(this.viewVisible ? Visibility.Hidden : Visibility.Visible)
      Stack({ alignContent: Alignment.TopStart }) {
        if (!(this.checkFinish() || this.disableAnim)) {
          Canvas(this.context)
            .width('100%')
            .height(this.pointHeight)
            .onAreaChange((oldArea, newArea) => {
              this.textPointManager.setThisCanvasWidth(newArea.width as number)
              Logger.info(TAG,
                `canvas newAreaWidth ${newArea.width as number} newAreaHeight ${newArea.height as number}`);
              Logger.info(TAG,
                `canvas oldAreaWidth ${oldArea.width as number} oldAreaHeight ${oldArea.height as number}`);
            })
        }
        Stack({ alignContent: Alignment.TopStart }) {
          Row() {
            this.innerComponent?.();
          }.margin(this.isAddTextArea ? { left: -12, right: -12 } : 0)
        }.onAreaChange((oldArea, newArea) => {
          let newAreaWidth = newArea.width as number;
          if (this.nowCanvasLine === 0) {
            // 为了第一行的宽度自适应且留出点阵的显示区域，需要先把第一行的最终宽度设置上
            this.canvasWidth = Math.min(Math.max(newAreaWidth, this.textPointManager.getMaxWidth()),
              this.parentMaxWidth - this.getPaddingStartEnd());
          } else {
            this.canvasWidth = newAreaWidth;
          }
          this.canvasWidth = Math.max(this.canvasWidth, DEFAULT_MIN_WIDTH)
          this.canvasHeight = newArea.height as number;
          this.lineCalc.onTextAreaChange(newAreaWidth, this.canvasHeight)
        })
      }
      .id('point_view.build.stack')
      .constraintSize({
        minHeight: 0,
        minWidth: DEFAULT_MIN_WIDTH
      })
      .padding(this.paddingValue)
      .onAreaChange((oldArea, newArea) => {
        this.areaWidth = this.parentMaxWidth;
        this.areaHeight = newArea.height as number;
        this.textPointManager.setThisAreaWidth(this.areaWidth);
        this.textPointManager.setThisAreaHeight(newArea.height as number);
        this.updatePointViewHeight(this.txt, this.areaWidth - this.getPaddingStartEnd());
      })
      .visibility(this.viewVisible ? Visibility.Visible : Visibility.None)
    }
    .backgroundColor('#00000000')
    .border(this.borderValue)
    .constraintSize({
      minHeight: 0,
      minWidth: DEFAULT_MIN_WIDTH
    })
  }


}

