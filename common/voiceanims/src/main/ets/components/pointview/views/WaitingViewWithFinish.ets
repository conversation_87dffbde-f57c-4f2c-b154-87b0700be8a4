import { Logger } from '../utils/Logger';
import { ViewDisplaySync } from './ViewDisplaySync';
import { systemDateTime } from '@kit.BasicServicesKit';
import { WaitingViewController } from './WaitingViewController';
import { DEFAULT_RADIUS_SIZE, MOVE_TO_CENTER, WaitPointDrawer } from './WaitPointDrawer';

const TAG = 'WaitingView';
const FRAME_FAST = 26;
const SLEEP_TIME = 83;
const FRAME_SLOW_ANIM_TIME = 350;

/**
 * 等待点阵和结束动画
 */
@Component
export struct WaitingViewWithFinish {
  private setting: RenderingContextSettings = new RenderingContextSettings(true)
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.setting); // 动效显示
  private loadingPoint:WaitPointDrawer | null = null;
  @Prop
  private pointSize: number = 20;
  private displaySync: ViewDisplaySync = new ViewDisplaySync(FRAME_FAST);
  private sleepStartTime = 0;
  @Prop
  radius: number = DEFAULT_RADIUS_SIZE;
  private waitDoAnimToFinish = false;

  private waitingViewController: WaitingViewController | null = null;

  showLoading() {
    let leftOffset = 0;
    let lineTop = 0;
    let textPointHeight = this.pointSize;
    let textPointWidth = this.pointSize;
    this.loadingPoint = new WaitPointDrawer(textPointWidth, textPointHeight)
    let textPoint = this.loadingPoint;
    textPoint.progress = 0.1;
    textPoint.xLeft = leftOffset;
    textPoint.top = lineTop;
    textPoint.startPoint = 0;
    textPoint.type = 5;
    textPoint.animTime = FRAME_SLOW_ANIM_TIME;
    textPoint.radius = this.radius;
    leftOffset = leftOffset + textPointWidth + (textPointWidth - 6 * textPoint.radius);
    textPoint.showCanvasWithFirstFrame(this.context);
  }

  aboutToAppear(): void {
    Logger.info(TAG, `aboutToAppear aboutToAppear`);
    this.showLoading();
    this.displaySync.createAndStartSync(() => {
      if(systemDateTime.getTime() - this.sleepStartTime <= SLEEP_TIME) {
        return;
      }
      this.doCircleInfinitelyAnim();
    })
    this.bindController();

  }

  bindController() {
    if (this.waitingViewController) {
      this.waitingViewController.doFinishAnim = this.doFinishAnim.bind(this);
      this.waitingViewController.isFinish = this.isFinish.bind(this);
    }
  }

  private doFinishAnim() {
    Logger.info(TAG, `doFinishAnim`);
    this.waitDoAnimToFinish = true;
  }

  private isFinish(): boolean {
    return this.loadingPoint?.moveType === MOVE_TO_CENTER && this.loadingPoint.progress >= 1.0;
  }

  doCircleInfinitelyAnim() {
    if(this.loadingPoint) {
      const textPoint = this.loadingPoint;
      textPoint?.showCanvas(this.context);
      if (this.waitDoAnimToFinish) {
        this.waitDoAnimToFinish = false;
        this.loadingPoint.doFinishAnim();
      }
    }
  }

  aboutToDisappear(): void {
    Logger.info(TAG, `aboutToAppear aboutToDisappear`);
    this.displaySync.stopSync();
  }

  build() {
    Canvas(this.context)
      .width(this.pointSize)
      .height(this.pointSize)
  }
}