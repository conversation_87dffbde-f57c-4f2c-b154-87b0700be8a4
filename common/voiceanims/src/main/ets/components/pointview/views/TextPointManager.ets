import { TextPointDrawer, radius } from './TextPointDrawerDot'
import { LineCalc } from './LineCalc'
import { Logger } from '../utils/Logger';
import measure from '@ohos.measure'
import systemDateTime from '@ohos.systemDateTime';
import { BASE_POINT_ANIM_TIME, TOTAL_SHOW_COUNT } from '../utils/ConstUtil';
import { LineMetricCallback } from './PointView';

const SHOW_POINT_TIME = 50;

const TAG = 'TextPointManager'



/**
 * 清除点阵时右边要多一点，不然文本和点阵边缘重叠
 */
const CLEAR_OFFSET = 24;

export const POINT_SHOW_SPEED = 25;

/**
 * 点阵管理类
 */
export class TextPointManager {
  private bindCanvasContext: CanvasRenderingContext2D;
  private isStopped: boolean = false;
  /**
   * 点阵的位置
   */
  private pointArray: Map<number, TextPointDrawer[]> = new Map();
  /**
   * key:行数
   * value: 最大宽度，vp
   */
  private lineMaxArray: Map<number, number> = new Map();
  /**
   * 所有行数中的最大宽度
   */
  private lineMaxWidth: number = 0;
  private currentRunLines: Set<number> = new Set();
  /**
   * 正常是canvas的宽度，也是txt控件的最大宽度
   */
  private thisAreaWidth: number = 0;
  /**
   * 正常是canvas的高度，也是txt控件的最大高度
   */
  private thisAreaHeight: number = 0;
  private thisCanvasWidth: number = 0;
  private lineCalc: LineCalc = new LineCalc();
  private nowShowLine: number = 0;
  private nowShowColumn: number = 0;
  private nowLineShowPointCount: number = 0;
  private oneLineColumn: number = 0;
  private nowShowOn: boolean = true;
  private pointAnimTime: number = BASE_POINT_ANIM_TIME;
  private baseBottomPadding: number = -1;
  private bottomExtraOffset: number = 0;

  private maxTotalShowCount = TOTAL_SHOW_COUNT;
  private lineMetricCallback? : LineMetricCallback;

  constructor(canvasContext: CanvasRenderingContext2D, lineCalc: LineCalc) {
    this.bindCanvasContext = canvasContext;
    this.lineCalc = lineCalc;
  }

  public setLineCalc(value: LineCalc) {
    this.lineCalc = value;
  }

  public setLineMetricCallback(lineMetricCallback: LineMetricCallback) {
    this.lineMetricCallback = lineMetricCallback;
  }

  public setThisAreaWidth(value: number) {
    this.thisAreaWidth = value;
    this.lineCalc?.setAreaWidth(value);
  }

  public setThisAreaHeight(value: number) {
    this.thisAreaHeight = value;
  }

  public setThisCanvasWidth(value: number) {
    this.thisCanvasWidth = value;
  }

  public setPointAnimTime(newTime: number) {
    this.pointAnimTime = newTime;
  }

  public setBottomOffset(bottomOffset: number) {
    this.bottomExtraOffset = bottomOffset;
  }

  public setNowShowLine(nowShowLine: number) {
    this.nowShowLine = nowShowLine;
    this.nowShowColumn = 0;
    Logger.info(TAG, `setNowShowLine nowShowLine  ${this.nowShowLine}`);
  }

  public setNowShowOn(nowShowOn: boolean) {
    this.nowShowOn = nowShowOn;
  }

  public clearOldShowLine(oldShowLine: number) {
    Logger.info(TAG, `clearOldShowLine ${oldShowLine}`);
    this.stopPointAnim(oldShowLine);
    let oldLineBottom = this.lineCalc.getLineBottom(oldShowLine);
    this.bindCanvasContext.clearRect(0, 0, this.thisAreaWidth, oldLineBottom);
  }

  public clearAllCanvas() {
    this.bindCanvasContext.clearRect(0, 0, vp2px(this.thisAreaWidth), vp2px(this.thisAreaHeight));
  }

  public clearOldLineBefore(nowShowLine: number) {
    if(nowShowLine > 0) {
      for (let index = 0; index < nowShowLine; index++) {
        this.clearOldShowLine(index)
      }
    }
  }

  public onFrame() {
    if (this.isStopped) {
      return;
    }
    this.bindCanvasContext.reset();
    this.currentRunLines.forEach((line) => {
      // 2 每4个点为一组，做动画，最后以灰色结尾
      this.showOneLinePoint(this.bindCanvasContext, line, 0, 0, vp2px(this.thisAreaWidth), 40);
      this.moveOneStep(line)
    })
  }

  moveOneStep(line: number) {
    const element = this.pointArray.get(line);
    if (element === null || element === undefined) {
      Logger.debug(TAG, `moveOneStep line ${line} element null`);
      return;
    }
    for (let column = 0; column < element.length; column++) {
      const target = element[column];
      if (!target) {
        continue
      }
      if (!target.haveShowed) {
        Logger.info(TAG, `moveOneStep line ${line} reach column ${column}`);
        return
      }
      if (target.firstShowTime === 0) {
        target.firstShowTime = systemDateTime.getTime();
      }
      target.progress = Math.max(0, Math.min((systemDateTime.getTime() - target.firstShowTime) / target.animTime, 1));
      Logger.debug(TAG, `moveOneStep line ${line} update column ${column} progress ${target.progress}`);
      if (target.progress <= 0.1) {
        return;
      }
    }
  }

  // 1 预先填满一行
  // 2 每4个点为一组，做动画，最后以灰色结尾
  // 3 文本覆盖canvas之后，擦除被覆盖的部分
  // 4 文本即将达到右边缘的时候，新一行的动画开始
  showPointAnim(line: number) {
    this.isStopped = false;
    Logger.info(TAG, `showPointAnim in ${line}`);
    if(!this.currentRunLines.has(line)) {
      this.currentRunLines.add(line);
    } else {
      Logger.info(TAG, `showPointAnim but currentRunLines have`);
    }
  }

  private stopPointAnim(line: number) {
    Logger.info(TAG, `stopPointAnim ${line}`);
    this.pointArray.delete(line);
    this.currentRunLines.delete(line)
  }

  /**
   * 这里都是px
   * @param left
   * @param top
   * @param right
   * @param bottom
   */
  showOneLinePoint(canvasContext: CanvasRenderingContext2D, line: number, left: number, top: number, right: number,
    bottom: number) {
    if (!this.nowShowOn) {
      return;
    }
    Logger.info(TAG, `showOneLinePoint in ${line} ${left} ${top} ${right} ${bottom}`);
    let thisLineHeight = this.lineCalc.getLineBottom(line) - this.lineCalc.getLineTop(line)
    if (thisLineHeight <= 0) {
      Logger.error(TAG, `showOneLinePoint thisLineHeight is 0`);
      return;
    }
    if (this.baseBottomPadding < 0) {
      let padding = (thisLineHeight - this.lineCalc.getOnePointHeight()) / 2;
      this.baseBottomPadding = padding + this.bottomExtraOffset;
    }
    // ux要求点阵是个正方形
    let textPointHeight = this.lineCalc.getOnePointHeight() ;
    let textPointWidth = textPointHeight;
    this.oneLineColumn = Math.floor(this.calcPointCount(px2vp(right - left), textPointWidth, radius));
    let totalCount = this.oneLineColumn;
    if (!this.pointArray.has(line)) {
      this.pointArray.set(line, new Array(totalCount));
    }

    canvasContext.save();
    let lineBottom = this.lineCalc.getLineBottom(line)
    let leftOffset = left;
    let thisLineMaxPointCount = this.getPointOneLineMaxCount(line);
    Logger.debug(TAG, `showOneLinePoint thisLineMaxPointCount ${thisLineMaxPointCount} line ${line} lineBottom ${lineBottom}`);

    let nowLineShowPoint = 0;
    let nowTxtWidth = this.getNowTxtWidthByLine(line);
    for (let index = 0; index < totalCount; index++) {
      let textPoint = this.findTarget(this.pointArray, line, index);
      if (textPoint === undefined) {
        textPoint = new TextPointDrawer();
        textPoint.row = line;
        textPoint.column = index;
        textPoint.progress = 0;
        textPoint.xLeft = leftOffset;
        // 算不准，只能调整
        textPoint.top = lineBottom - this.baseBottomPadding - textPointHeight;
        textPoint.startPoint = index % 4;
        // 算不准，只能调整
        textPoint.height = textPointHeight;
        textPoint.width = textPointWidth;
        textPoint.animTime = this.pointAnimTime
        textPoint.type = index % 8;
        if (textPoint.xLeft + textPoint.width > this.thisCanvasWidth) {
          continue
        }
        // 文本已经显示了很多了，已经显示文本的地方就不用再显示了
        if (textPoint.xLeft + textPoint.width < nowTxtWidth) {
          textPoint.cleared = true;
        }

        let linePoint = this.pointArray.get(line);
        if (linePoint !== null && linePoint !== undefined && linePoint.length > index) {
          linePoint[index] = textPoint;
          Logger.debug(TAG, `showOneLinePoint add linePoint index ${index}`)
        }
        leftOffset = leftOffset + textPointWidth + (textPointWidth - 6 * radius);
      } else {
        textPoint.top = lineBottom - this.baseBottomPadding - textPointHeight;
      }

      // 一行的话，只显示最多TOTAL_SHOW_COUNT个， 另一行显示一半
      if (line === this.nowShowLine) {
        if (index - this.nowShowColumn < this.getMaxTotalShowCount() && index < thisLineMaxPointCount) {
          textPoint.showCanvasWithProgress(canvasContext);
        }
        if (!textPoint.cleared && textPoint.progress > 0) {
          nowLineShowPoint++;
        }
      } else {
        let nextLineMaxCount = this.getMaxTotalShowCount() - this.nowLineShowPointCount;
        if (index < nextLineMaxCount) {
          textPoint.showCanvasWithProgress(canvasContext);
        }
      }
    }
    if (line === this.nowShowLine) {
      this.nowLineShowPointCount = nowLineShowPoint;
    }
    canvasContext.restore();
  }

  getNowTxtWidthByLine(line: number): number {
    if(!this.lineMetricCallback) {
      return 0;
    }
    if (line < 0 || line > this.lineMetricCallback.getLineCount() - 1) {
      return 0;
    }
    return px2vp(this.lineMetricCallback.getLastLineMetric(line)?.width);
  }

  getMaxTotalShowCount(): number {
    return this.maxTotalShowCount;
  }

  setMaxTotalShowCount(maxTotalShowCount: number) {
    this.maxTotalShowCount = maxTotalShowCount;
  }

  calcPointCount(parentWidthVp: number, onePointWidth: number, radius: number) {
    // 这里通过公式转换来的
    // width = x * onePointViewSizeVp + y * offset
    // x = y + 1
    // offset = onePointViewSizeVp - 6 * radius
    return (parentWidthVp + onePointWidth - 6 * radius) / 2 / (onePointWidth - 3 * radius);
  }

  findTarget(targetArray: Map<number, TextPointDrawer[]>, row: number, column: number): TextPointDrawer | undefined {
    if (!targetArray.has(row)) {
      return undefined;
    }
    let lineArray = targetArray.get(row);
    if (lineArray === undefined || lineArray[column] === null || lineArray[column] === undefined) {
      return undefined;
    }
    return lineArray[column];
  }

  public reset() {
    this.pointArray.clear();
  }

  /**
   * 通知更新最后一行文本
   * @param allTxt 所有文本
   * @param thisLineStartPos 这行文本的启动位置
   * @param nowIndex 当前位置
   * @param thisLine 当前行数
   */
  public updateLineText(allTxt: string, thisLineStartPos: number, nowIndex: number, thisLine: number,
    fontSize: string) {
    // 获取当前显示的这行文本
    if(this.lineCalc.getLineMetrics() === undefined) {
      return;
    }
    let lastLineTxt = allTxt.substring(thisLineStartPos, nowIndex);
    this.updateTextCoverByLastLineText(lastLineTxt, thisLine, fontSize);
    let right = this.lineCalc?.getLineRightMax(thisLineStartPos, allTxt) ?? 0;
    Logger.debug(TAG, `updateLineText right ${right} line ${thisLine} lastLineTxt ${lastLineTxt}`);

    if (right > thisLineStartPos) {
      let thisLineMaxTxt = allTxt.substring(thisLineStartPos, right);
      let txtWidth = this.measureTextWidth(thisLineMaxTxt, fontSize);
      let txtWidthVp = px2vp(txtWidth);
      this.lineMaxArray.set(thisLine, txtWidthVp);
      let oneCharWidth = 0;
      if (this.lineCalc) {
        oneCharWidth = this.lineCalc.getOneCharWidth();
      }
      // 因为text组件最后一个字符不够完全显示的话会换行，所以算出来的永远大于实际显示的，所以需要减去一点阈值
      this.lineMaxWidth = Math.max(this.lineMaxWidth, txtWidthVp - oneCharWidth * 2)
      Logger.debug(TAG, `updateLineText txtWidth VP ${txtWidthVp} line ${thisLine} thisLineMaxTxt ${thisLineMaxTxt}`);
    }
    this.showNextLineIfNeed(nowIndex, right, thisLine + 1, allTxt.length);
  }

  private getPointOneLineMaxCount(line: number) {
    let maxWidth = this.getPointMaxWidth(line);
    let textPointHeight = this.lineCalc?.getOnePointHeight() ?? 0;
    let textPointWidth = textPointHeight;
    return Math.ceil(this.calcPointCount(maxWidth, textPointWidth, radius));
  }

  private getPointMaxWidth(line: number): number {
    if (this.lineMaxArray.has(line) && this.lineMaxArray.get(line)) {
      return this.lineMaxArray.get(line) ?? this.thisAreaWidth;
    }
    return this.lineCalc?.getNowTextAreaWidth() ?? this.thisAreaWidth;
  }

  public getMaxWidth(): number {
    return this.lineMaxWidth;
  }

  public clearMaxWidth() {
    Logger.debug(TAG, `clearMaxWidth ${this.lineMaxWidth}`);
    this.lineMaxWidth = 0;
  }

  public updateTextCoverByLastLineText(txt: string, line: number, fontSize: string) {
    Logger.debug(TAG, `updateTextCoverByLastLineText = ${txt} line ${line}`);
    this.updateTextCover(line);
  }

  private measureTextWidth(txt: string, fontSize: string): number {
    if (!txt || txt === '') {
      return 0;
    }
    return measure.measureText({
      textContent: txt,
      fontSize: fontSize
    });
  }

  /**
   * 按需显示下一行
   * @param txtIndexNow 当前index
   * @param thisLineMaxRightIndex 当前行的最右边的文本index
   * @param nexLine 下一行的行数
   */
  private showNextLineIfNeed(txtIndexNow: number, thisLineMaxRightIndex: number, nexLine: number,
    allTxtLength: number) {
    Logger.debug(TAG,
      `showNextLineIfNeed txtIndexNow ${txtIndexNow} thisLineMaxRightIndex ${thisLineMaxRightIndex} nexLine ${nexLine}`);
    if (txtIndexNow + this.getMaxTotalShowCount() < thisLineMaxRightIndex) {
      return;
    }
    // 没有下一行了 所有文本都结束了
    if (txtIndexNow + this.getMaxTotalShowCount() >= allTxtLength) {
      return;
    }

    // 第一行还未开始,先不绘制下一行
    if (nexLine <= 1 && thisLineMaxRightIndex <= 0) {
      return;
    }
    if (this.isThisLineHaveStarted(nexLine)) {
      return;
    }
    this.showPointAnim(nexLine);
  }

  /**
   * 清除被文本覆盖的部分
   * @param line 对应的行数
   */
  public updateTextCover(line: number) {
    let lineMetricsWidth = px2vp(this.lineCalc.getLineMetrics()?.width) || 0
    let vpTxtWidth = lineMetricsWidth + CLEAR_OFFSET;
    let remainder = vpTxtWidth % this.lineCalc.getOnePointHeight();
    vpTxtWidth = vpTxtWidth + (this.lineCalc.getOnePointHeight() - remainder);
    Logger.info(TAG, `updateTextCover vpTxtWidth ${vpTxtWidth} line ${line}`);
    let thisLineBottom = this.lineCalc.getLineBottom(line);
    this.clearCanvas(this.bindCanvasContext, thisLineBottom, vpTxtWidth);
    this.nowShowColumn = this.calcPointCount(vpTxtWidth, this.lineCalc?.getOnePointHeight() ?? 0, radius);

    // 标记对象，不要再绘制了
    const element = this.pointArray.get(line);
    if (element === null || element === undefined) {
      return;
    }
    let maxClearWidth = 0;
    for (let column = 0; column < element.length; column++) {
      const target = element[column];
      if (!target) {
        continue
      }
      Logger.debug(TAG, `clearTextCover line ${line} column ${column} xleft ${target.xLeft} vp ${vpTxtWidth}`)
      if (target.xLeft <= vpTxtWidth) {
        target.cleared = true;
        target.haveShowed = true;
      }
    }
    this.clearCanvas(this.bindCanvasContext, thisLineBottom, maxClearWidth);
  }

  /**
   * 这一行是否已经开始
   * @param line 检查的行数
   * @returns true 已经开始
   */
  public isThisLineHaveStarted(line: number): boolean {
    if (this.pointArray.has(line)) {
      return true;
    }
    return false;
  }

  private clearCanvas(drawContext: CanvasRenderingContext2D, bottom: number, right: number) {
    Logger.info(TAG, `clearCanvas bottom ${bottom} right ${right}`);
    drawContext.clearRect(0, 0, right, bottom);
    return;
  }

  private clearAll() {
    Logger.info(TAG, 'clearAll');
    if (this.lineCalc) {
      this.lineCalc.reset();
    }
    // this.bindCanvasContext.clearRect(0, 0, this.thisAreaWidth, this.thisAreaHeight);
    this.bindCanvasContext.reset()
  }

  /**
   * 清除所有
   */
  public stopAndClear() {
    Logger.info(TAG, 'stopAll');
    this.isStopped = true;
    this.clearAll();
    this.pointArray.clear();
    this.currentRunLines.clear();
  }
}