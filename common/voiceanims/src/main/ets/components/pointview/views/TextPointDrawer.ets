import { Logger } from '../utils/Logger';
import systemDateTime from '@ohos.systemDateTime';
import { BASE_POINT_ANIM_TIME } from '../utils/ConstUtil';

const color1 = '#F79D34'
const color1Night = '#F4B144'
const color2 = '#F4785A'
const color3 = '#625CF9'
const color3Night = '#AC7DF9'
const color4 = '#5DACF7'
const color5 = '#7F000000'
const colorNight5 = '#7FFFFFFF'

export const radius = 1.25;

const TAG = 'TextPointDrawer'

const STORAGE_KEY_IS_DARK_MODE: string = 'VA_IS_DARK_MODE';

/**
 * 单个点阵的对象，包含四个点
 */
export class TextPointDrawer {
  // 是否被清除
  cleared: boolean = false;
  row: number = 0;
  column: number = 0;
  // 左边的位置
  xLeft: number = 0;
  top: number = 0
  // 进度 0 -1
  progress: number = 1;
  firstShowTime: number = 0;
  // 从哪个点开始启动
  startPoint: number = 0;
  // 为文本行高
  height: number = 10;
  width: number = 0;
  type: number = 0;
  // 是否显示过
  haveShowed: boolean = false;
  animTime: number = BASE_POINT_ANIM_TIME;
  moveType: number = 0;

  showCanvasWithProgress(drawContext: CanvasRenderingContext2D) {
    let textPoint = this;
    if (textPoint.cleared) {
      Logger.debug(TAG, `showCanvasWithProgress cleared row ${this.row} column ${this.column}`);
      return;
    }
    textPoint.haveShowed = true;
    if (this.progress <= 0) {
      Logger.debug(TAG, `showCanvasWithProgress this.progress is 0 row ${this.row} column ${this.column}`);
      return;
    }
    drawContext.save();
    Logger.debug(TAG,
      `showCanvasWithProgress type ${this.type} row ${this.row} column ${this.column} progress ${textPoint.progress} ${this.xLeft}-${this.top}-${this.xLeft +
      this.width}-${this.top + this.height}`);
    switch (this.type) {
      case 0:
        this.drawTypeOne(drawContext);
        break;
      case 1:
        this.drawTypeTwo(drawContext);
        break;
      case 2:
        this.drawTypeThree(drawContext);
        break;
      case 3:
        this.drawTypeFour(drawContext);
        break;
      case 4:
        this.drawTypeFive(drawContext);
        break;
      case 5:
        this.drawTypeSix(drawContext);
        break;
      case 6:
        this.drawTypeWait(drawContext);
        break;
    }
    drawContext.restore();
  }

  doDrawOnePoint(drawContext: CanvasRenderingContext2D, onePoint: OnePoint) {
    if (this.progress < onePoint.getStartProgress()) {
      return;
    }
    let resultLocation = onePoint.calcProgress(this.progress);
    drawContext.beginPath();
    drawContext.arc(this.xLeft + radius / 2 + resultLocation.x, this.top + radius / 2 + resultLocation.y,
      radius * resultLocation.scale, 0, 3.1415 * 2, false);
    drawContext.fillStyle = onePoint.color;
    drawContext.fill();
  }

  drawTypeOne(drawContext: CanvasRenderingContext2D) {
    // 这里实测底部要距离2个radius，按道理一个就行了
    let point0: OnePoint = new OnePointOneDirection(radius, radius, radius, radius, 0, 1, this.getColor1());
    let point1: OnePoint =
      new OnePointTwoDirection(radius, radius, radius, this.height - 2 * radius, this.width - 2 * radius,
        this.height - 2 * radius, 0, 1, this.getColor3());
    let point2: OnePoint =
      new OnePointOneDirection(radius, radius, this.width - 2 * radius, radius, 0.25, 0.75, color4);
    let point3: OnePoint = new OnePointOneDirection(radius, radius, radius, this.height - 2 * radius, 0.5, 1, color2);
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
  }

  getColor1(): string {
    let isDarkMode = AppStorage.get<boolean>(STORAGE_KEY_IS_DARK_MODE);
    if (isDarkMode) {
      return color1Night
    }
    return color1;
  }

  getColor3(): string {
    let isDarkMode = AppStorage.get<boolean>(STORAGE_KEY_IS_DARK_MODE);
    if (isDarkMode) {
      return color3Night
    }
    return color3;
  }

  drawTypeTwo(drawContext: CanvasRenderingContext2D) {
    // 这里实测底部要距离2个radius，按道理一个就行了
    let point0: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, radius, this.width - 2 * radius, radius, 0, 1, color4);
    let point1: OnePoint =
      new OnePointTwoDirection(this.width - 2 * radius, radius, radius, radius, radius, this.height - 2 * radius, 0, 1,
        color2);
    let point2: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, radius, this.width - 2 * radius, this.height - 2 * radius, 0.25,
        0.75, this.getColor3());
    let point3: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, radius, radius, radius, 0.5, 1, this.getColor1());
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
  }

  drawTypeThree(drawContext: CanvasRenderingContext2D) {
    // 这里实测底部要距离2个radius，按道理一个就行了
    let point0: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, this.height - 2 * radius, this.width - 2 * radius,
        this.height - 2 * radius, 0, 1, color2);
    let point1: OnePoint =
      new OnePointTwoDirection(this.width - 2 * radius, this.height - 2 * radius, this.width - 2 * radius, radius,
        radius, radius, 0, 1, this.getColor3());
    let point2: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, this.height - 2 * radius, radius, this.height - 2 * radius,
        0.25, 0.75, this.getColor1());
    let point3: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, this.height - 2 * radius, this.width - 2 * radius, radius, 0.5,
        1, color4);
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
  }

  drawTypeFour(drawContext: CanvasRenderingContext2D) {
    // 这里实测底部要距离2个radius，按道理一个就行了
    let point0: OnePoint =
      new OnePointOneDirection(radius, this.height - 2 * radius, radius, this.height - 2 * radius, 0, 1, color4);
    let point1: OnePoint =
      new OnePointTwoDirection(radius, this.height - 2 * radius, this.width - 2 * radius, this.height - 2 * radius,
        this.width - 2 * radius, radius, 0, 1, color2);
    let point2: OnePoint =
      new OnePointOneDirection(radius, this.height - 2 * radius, radius, radius, 0.25, 0.75, this.getColor3());
    let point3: OnePoint =
      new OnePointOneDirection(radius, this.height - 2 * radius, this.width - 2 * radius, this.height - 2 * radius, 0.5,
        1, this.getColor1());
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
  }

  drawTypeFive(drawContext: CanvasRenderingContext2D) {
    // 这里实测底部要距离2个radius，按道理一个就行了
    let point0: OnePoint =
      new OnePointTwoDirection(radius, this.height - 2 * radius, radius, this.height - 2 * radius, radius, radius, 0, 1,
        this.getColor3());
    let point1: OnePoint =
      new OnePointOneDirection(radius, this.height - 2 * radius, this.width - 2 * radius, this.height - 2 * radius, 0,
        0.5, color4);
    let point2: OnePoint =
      new OnePointOneDirection(radius, this.height - 2 * radius, radius, this.height - 2 * radius, 0.5, 1,
        this.getColor1());
    let point3: OnePoint =
      new OnePointOneDirection(this.width - 2 * radius, this.height - 2 * radius, this.width - 2 * radius, radius, 0.5,
        1, color2);
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
  }

  drawTypeSix(drawContext: CanvasRenderingContext2D) {
    // 这里实测底部要距离2个radius，按道理一个就行了
    let point0: OnePoint = new OnePointOneDirection(radius, radius, radius, radius, 0, 1, this.getColor1());
    let point1: OnePoint =
      new OnePointTwoDirection(radius, radius, radius, this.height - 2 * radius, this.width - 2 * radius,
        this.height - 2 * radius, 0, 1, this.getColor3());
    let point2: OnePoint = new OnePointOneDirection(radius, radius, this.width - 2 * radius, radius, 0.4, 1, color4);
    let point3: OnePoint = new OnePointOneDirection(radius, radius, radius, this.height - 2 * radius, 0.5, 1, color2);
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
  }

  drawTypeWait(drawContext: CanvasRenderingContext2D) {
    let point0: OnePoint =
      new MovingPoint(new DrawRect(radius, radius, this.width - 2 * radius, this.height - 2 * radius), this.moveType,
        this.getColor1());
    let point1: OnePoint =
      new MovingPoint(new DrawRect(radius, radius, this.width - 2 * radius, this.height - 2 * radius),
        (this.moveType + 1) % 4, this.getColor3());
    let point2: OnePoint =
      new MovingPoint(new DrawRect(radius, radius, this.width - 2 * radius, this.height - 2 * radius),
        (this.moveType + 2) % 4, color4);
    let point3: OnePoint =
      new MovingPoint(new DrawRect(radius, radius, this.width - 2 * radius, this.height - 2 * radius),
        (this.moveType + 3) % 4, color2);
    this.doDrawPoints(drawContext, [point3, point2, point1, point0]);
    if (MovingPoint.canToNext(this.progress)) {
      this.toNextMode();
    }
  }

  doDrawPoints(drawContext: CanvasRenderingContext2D, points: OnePoint[]) {
    drawContext.save();
    points.forEach((onePoint) => {
      this.doDrawOnePoint(drawContext, onePoint);
    })
    drawContext.restore();
  }

  private toNextMode() {
    let nextType = this.moveType + 1;
    this.moveType = nextType % 4;
  }
}

class ResultLocation {
  x = 0;
  y = 0;
  scale = 1;

  constructor(x: number, y: number, scale: number = 1) {
    this.x = x;
    this.y = y;
    this.scale = scale;
  }
}

abstract class OnePoint {
  startProgress: number;
  endProgress: number;
  color = '#ffffff';

  constructor(startProgress: number, endProgress: number, color: string) {
    this.startProgress = startProgress;
    this.endProgress = endProgress;
    this.color = color;
  }

  calcProgress(progress: number): ResultLocation {
    return new ResultLocation(0, 0);
  }

  getStartProgress() {
    return this.startProgress;
  }

  getEndProgress() {
    return this.endProgress;
  }

  getEndColor(isDarkMode?: boolean): string {
    return isDarkMode ? colorNight5 : color5
  }
}

/**
 * 动画运动一段，A-B
 */
class OnePointOneDirection extends OnePoint {
  startX = 0;
  startY = 0;
  endX = 0;
  endY = 0;

  constructor(startX: number, startY: number, endX: number, endY: number, startProgress: number, endProgress: number,
    color: string) {
    super(startProgress, endProgress, color);
    this.startX = startX;
    this.startY = startY;
    this.endX = endX;
    this.endY = endY;
  }

  calcProgress(progress: number): ResultLocation {
    let thisPointCurrentProgress = 0;
    if (progress > this.getEndProgress()) {
      thisPointCurrentProgress = 1;
    } else {
      let thisPointAllProgress = this.getEndProgress() - this.getStartProgress();
      thisPointAllProgress = thisPointAllProgress <= 0 ? 1 : thisPointAllProgress;
      thisPointCurrentProgress = (progress - this.getStartProgress()) / thisPointAllProgress;
    }

    let x = this.startX;
    let y = this.startY;
    x = this.startX + (this.endX - this.startX) * thisPointCurrentProgress;
    y = this.startY + (this.endY - this.startY) * thisPointCurrentProgress;
    return new ResultLocation(x, y);
  }
}

/**
 * 动画运动两段式的，A-B-C
 */
class OnePointTwoDirection extends OnePoint {
  startX = 0;
  startY = 0;
  middleX = 0;
  middleY = 0;
  endX = 0;
  endY = 0;

  constructor(startX: number, startY: number, middleX: number, middleY: number, endX: number, endY: number,
    startProgress: number, endProgress: number, color: string) {
    super(startProgress, endProgress, color);
    this.startX = startX;
    this.startY = startY;
    this.middleX = middleX;
    this.middleY = middleY;
    this.endX = endX;
    this.endY = endY;
    this.color = color;
  }

  calcProgress(progress: number): ResultLocation {
    let thisPointCurrentProgress = 0;
    if (progress > this.getEndProgress()) {
      thisPointCurrentProgress = 1;
    } else {
      let thisPointAllProgress = this.getEndProgress() - this.getStartProgress();
      thisPointAllProgress = thisPointAllProgress <= 0 ? 1 : thisPointAllProgress;
      thisPointCurrentProgress = (progress - this.getStartProgress()) / thisPointAllProgress;
    }

    let x = this.startX;
    let y = this.startY;
    if (thisPointCurrentProgress < 0.5) {
      x = this.startX + (this.middleX - this.startX) * thisPointCurrentProgress * 2;
      y = this.startY + (this.middleY - this.startY) * thisPointCurrentProgress * 2;
    } else if (thisPointCurrentProgress > 0.5) {
      x = this.middleX + (this.endX - this.middleX) * (thisPointCurrentProgress - 0.5) * 2;
      y = this.middleY + (this.endY - this.middleY) * (thisPointCurrentProgress - 0.5) * 2;
    } else {
      x = this.middleX;
      y = this.middleY;
    }
    return new ResultLocation(x, y);
  }
}

class MovingPoint extends OnePoint {
  afterEndAnimator: AfterEndAnimator;

  constructor(rect: DrawRect, initMoveType: number, color: string) {
    super(0, 0, color)
    this.afterEndAnimator = new AfterEndAnimator(0, 0, rect, initMoveType);
  }

  static canToNext(progress: number): boolean {
    if (progress % 1 >= 0.90) {
      return true;
    }
    return false;
  }

  calcProgress(progress: number): ResultLocation {
    return this.afterEndAnimator.calcProgress(progress % 1)
  }

  getEndColor(isDarkMode?: boolean): string {
    return this.color;
  }
}

class DrawRect {
  left: number;
  top: number;
  right: number;
  bottom: number;

  constructor(left: number, top: number, right: number, bottom: number) {
    this.left = left;
    this.top = top;
    this.right = right;
    this.bottom = bottom;
  }
}

const MOVE_LEFT_TO_RIGHT = 0;
const MOVE_TOP_TO_BOTTOM = 1;
const MOVE_RIGHT_TO_LEFT = 2;
const MOVE_BOTTOM_TO_TOP = 3;

class AfterEndAnimator {
  drawRect: DrawRect;
  moveType: number;
  x: number = 0;
  y: number = 0;

  constructor(x: number, y: number, drawRect: DrawRect, moveType: number) {
    this.drawRect = drawRect;
    this.moveType = moveType;
    this.x = x;
    this.y = y;
  }

  /**
   * 根据进度计算位置
   * @param progress 0~1
   * @returns
   */
  calcProgress(progress: number): ResultLocation {
    let resultX = 0;
    let resultY = 0;
    let withWaitProgress = Math.min(progress, 1);
    switch (this.moveType) {
      case MOVE_LEFT_TO_RIGHT:
        resultX = this.drawRect.left + (this.drawRect.right - this.drawRect.left) * withWaitProgress
        resultY = this.drawRect.top;
        break;
      case MOVE_TOP_TO_BOTTOM:
        resultX = this.drawRect.right;
        resultY = this.drawRect.top + (this.drawRect.bottom - this.drawRect.top) * withWaitProgress
        break;
      case MOVE_RIGHT_TO_LEFT:
        resultX = this.drawRect.right + (this.drawRect.left - this.drawRect.right) * withWaitProgress
        resultY = this.drawRect.bottom;
        break;
      case MOVE_BOTTOM_TO_TOP:
        resultX = this.drawRect.left;
        resultY = this.drawRect.bottom + (this.drawRect.top - this.drawRect.bottom) * withWaitProgress
        break;
    }
    let scale = 0.9 + Math.abs(0.5 - progress) * 2 * 0.1;
    return new ResultLocation(resultX, resultY, scale)
  }
}
