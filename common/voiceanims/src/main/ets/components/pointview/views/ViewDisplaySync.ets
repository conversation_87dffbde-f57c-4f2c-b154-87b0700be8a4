import displaySync from '@ohos.graphics.displaySync';
import {InterfaceDisplaySync} from './InterfaceDisplaySync';

export class ViewDisplaySync implements InterfaceDisplaySync {
  private backDisplaySync? : displaySync.DisplaySync = undefined;
  private sync60: ExpectedFrameRateRange = { expected: 60, min: 0, max: 120 };

  private expectedFrame: number = 60;

  public constructor(expectedFrame: number) {
    this.expectedFrame = expectedFrame;
  }

  public createAndStartSync(callback: Callback<object>) {
    if (!this.backDisplaySync) {
      this.backDisplaySync = displaySync.create()
      this.sync60.expected = this.expectedFrame;
      this.backDisplaySync.setExpectedFrameRateRange(this.sync60);
      this.backDisplaySync.on('frame', callback);
      this.backDisplaySync.start();
    }
  }

  public updateExpectedFrame(expectedFrame: number) {
    this.expectedFrame = expectedFrame;
    this.sync60.expected = this.expectedFrame;
    if (this.backDisplaySync) {
      this.backDisplaySync.setExpectedFrameRateRange(this.sync60);
    }
  }

  public stopSync() {
    this.backDisplaySync?.stop();
    this.backDisplaySync?.off('frame');
    this.backDisplaySync = undefined;
  }
}