import { Logger } from '../utils/Logger';
import systemDateTime from '@ohos.systemDateTime';
import { BASE_POINT_ANIM_TIME } from '../utils/ConstUtil';
import Curves from '@ohos.curves'

const color1 = '#F79D34'
const color1Night = '#F4B144'
const color2 = '#F4785A'
const color3 = '#625CF9'
const color3Night = '#AC7DF9'
const color4 = '#5DACF7'

export const DEFAULT_RADIUS_SIZE = 2;

const TAG = 'WaitPointDrawer'

const STORAGE_KEY_IS_DARK_MODE: string = 'VA_IS_DARK_MODE';
const ROTATE_INTERPOLATOR = Curves.cubicBezierCurve(0.33, 0.00, 0.67, 1.0);

const MOVE_LEFT_TO_RIGHT = 0;
const MOVE_TOP_TO_BOTTOM = 1;
const MOVE_RIGHT_TO_LEFT = 2;
const MOVE_BOTTOM_TO_TOP = 3;
export const MOVE_TO_CENTER = 10;

const NEXT_WAIT_TIME = 75;

/**
 * 等待点阵的的对象，包含四个点和结束动画
 */
export class WaitPointDrawer {
  // 是否被清除
  cleared: boolean = false;
  // 左边的位置
  xLeft: number = 0;
  top: number = 0
  // 进度 0 -1
  progress: number = 1;

  startTime:number = 0

  firstShowTime : number = 0;
  // 从哪个点开始启动
  startPoint: number = 0;
  // 为文本行高
  height: number = 10;
  width: number = 0;
  type: number = 0;
  // 是否显示过
  haveShowed: boolean = false;
  animTime :number = BASE_POINT_ANIM_TIME;

  animToCenter: boolean = false;
  moveType: number = 0;
  radius:number = DEFAULT_RADIUS_SIZE;
  private point0: OnePoint;
  private point1: OnePoint;
  private point2: OnePoint;
  private point3: OnePoint;

  constructor(width: number, height: number) {
    this.width = width;
    this.height = height;
    this.point0 =
      new OnePoint(new DrawRect(this.radius, this.radius, this.width - 2 * this.radius, this.height - 2 * this.radius),
        this.moveType, this.getColor1());
    this.point1 =
      new OnePoint(new DrawRect(this.radius, this.radius, this.width - 2 * this.radius, this.height - 2 * this.radius),
        (this.moveType + 1) % 4, this.getColor3());
    this.point2 =
      new OnePoint(new DrawRect(this.radius, this.radius, this.width - 2 * this.radius, this.height - 2 * this.radius),
        (this.moveType + 2) % 4, color4);
    this.point3 =
      new OnePoint(new DrawRect(this.radius, this.radius, this.width - 2 * this.radius, this.height - 2 * this.radius),
        (this.moveType + 3) % 4, color2);
  }


  showCanvasWithFirstFrame(drawContext: CanvasRenderingContext2D) {
    let textPoint = this;
    textPoint.haveShowed = true;
    this.progress = 0;
    this.startTime = systemDateTime.getTime();
    this.clear(drawContext)
    drawContext.save();
    Logger.debug(TAG,
      `showCanvasWithProgress type ${this.type}progress ${textPoint.progress} ${this.xLeft}-${this.top}-${this.xLeft +
      this.width}-${this.top + this.height}`);
    this.drawTypeWait(drawContext);
    drawContext.restore();
  }

  showCanvas(drawContext: CanvasRenderingContext2D) {
    let textPoint = this;
    textPoint.haveShowed = true;
    let elapseTime = systemDateTime.getTime() - this.startTime;
    if (elapseTime <= NEXT_WAIT_TIME) {
      this.progress = 0;
    } else {
      this.progress = (elapseTime - NEXT_WAIT_TIME) / this.animTime;
    }
    this.clear(drawContext)
    drawContext.save();
    Logger.debug(TAG,
      `showCanvasWithProgress type ${this.type}progress ${textPoint.progress} ${this.xLeft}-${this.top}-${this.xLeft +
      this.width}-${this.top + this.height}`);
    this.drawTypeWait(drawContext);
    drawContext.restore();
  }

  getColor1() : string {
    let isDarkMode = AppStorage.get<boolean>(STORAGE_KEY_IS_DARK_MODE);
    if (isDarkMode) {
      return color1Night
    }
    return color1;
  }

  getColor3() : string {
    let isDarkMode = AppStorage.get<boolean>(STORAGE_KEY_IS_DARK_MODE);
    if (isDarkMode) {
      return color3Night
    }
    return color3;
  }

  drawTypeWait(drawContext: CanvasRenderingContext2D) {
    this.point0.updateMoveType(this.moveType === MOVE_TO_CENTER ? MOVE_TO_CENTER : this.moveType)
    this.point1.updateMoveType(this.moveType === MOVE_TO_CENTER ? MOVE_TO_CENTER : (this.moveType + 1) % 4)
    this.point2.updateMoveType(this.moveType === MOVE_TO_CENTER ? MOVE_TO_CENTER : (this.moveType + 2) % 4)
    this.point3.updateMoveType(this.moveType === MOVE_TO_CENTER ? MOVE_TO_CENTER : (this.moveType + 3) % 4)

    this.doDrawPoints(drawContext, [this.point3, this.point2, this.point1, this.point0]);
    if (this.canToNext(this.progress)) {
      this.toNextMode();
    }
  }


  canToNext(progress: number) : boolean {
    if (progress >= 0.99) {
      return true;
    }
    return false;
  }

  public doFinishAnim() {
    this.animToCenter = true;
  }

  doDrawPoints(drawContext: CanvasRenderingContext2D, points : OnePoint[]) {
    this.clear(drawContext)
    drawContext.save();
    points.forEach((onePoint, index) => {
      this.doDrawOnePoint(drawContext, onePoint);
      Logger.debug(TAG, `doDrawPoints index ${index} x = ${onePoint.lastX} y = ${onePoint.lastY}`);
    })
    drawContext.restore();
  }

  doDrawOnePoint(drawContext: CanvasRenderingContext2D, onePoint: OnePoint) {
    if (this.progress < onePoint.getStartProgress()) {
      return;
    }
    let runProgress = ROTATE_INTERPOLATOR.interpolate(this.progress);
    let resultLocation = onePoint.calcProgress(onePoint, runProgress);
    drawContext.beginPath();
    let targetX = this.xLeft + this.radius/2 + resultLocation.x;
    let targetY = this.top + this.radius/2 + resultLocation.y;
    onePoint.lastX = resultLocation.x;
    onePoint.lastY = resultLocation.y;
    drawContext.arc(targetX, targetY , this.radius * resultLocation.scale, 0, 3.1415 * 2, false);
    drawContext.fillStyle = onePoint.color;
    drawContext.fill();
  }

  clear(drawContext: CanvasRenderingContext2D) {
    drawContext.clearRect(this.xLeft - this.radius / 2, this.top - this.radius / 2,
      this.xLeft + this.width + this.radius / 2, this.top + this.height + this.radius / 2);
  }

  private toNextMode() {
    if (this.moveType === MOVE_TO_CENTER) {
      return;
    }
    this.progress = 0;
    this.startTime = systemDateTime.getTime();
    if(this.animToCenter) {
      this.point0.toCenterStartX = this.point0.lastX;
      this.point0.toCenterStartY = this.point0.lastY;

      this.point1.toCenterStartX = this.point1.lastX;
      this.point1.toCenterStartY = this.point1.lastY;

      this.point2.toCenterStartX = this.point2.lastX;
      this.point2.toCenterStartY = this.point2.lastY;

      this.point3.toCenterStartX = this.point3.lastX;
      this.point3.toCenterStartY = this.point3.lastY;
      this.moveType = MOVE_TO_CENTER;
      return;
    }
    let nextType = this.moveType + 1;
    this.moveType = nextType % 4;
  }
}

class ResultLocation {
  x = 0;
  y = 0;
  scale = 1;

  constructor(x: number, y: number, scale : number = 1) {
    this.x = x;
    this.y = y;
    this.scale = scale;
  }
}

class OnePoint {
  startProgress: number;
  endProgress: number;
  color = '#ffffff';
  lastX: number = 0;
  lastY: number = 0;
  afterEndAnimator: AfterEndAnimator;
  toCenterStartX: number = 0;
  toCenterStartY: number = 0;

  constructor(rect: DrawRect, initMoveType: number, color: string) {
    this.startProgress = 0;
    this.endProgress = 1;
    this.color = color;
    this.afterEndAnimator = new AfterEndAnimator(rect, initMoveType);
  }
  public updateMoveType(newMoveType: number) {
    this.afterEndAnimator.updateMoveType(newMoveType);
  }

  getStartProgress() {
    return this.startProgress;
  }

  getEndProgress() {
    return this.endProgress;
  }

  calcProgress(onePoint: OnePoint, progress: number): ResultLocation {
    return this.afterEndAnimator.calcProgress(onePoint, progress)
  }
}


class DrawRect {
  left: number;
  top: number;
  right: number;
  bottom: number;

  constructor(left: number, top: number, right: number, bottom: number) {
    this.left = left;
    this.top = top;
    this.right = right;
    this.bottom = bottom;
  }
}


class AfterEndAnimator {
  drawRect: DrawRect;
  moveType: number;
  constructor(drawRect: DrawRect,moveType: number) {
    this.drawRect = drawRect;
    this.moveType = moveType;
  }
  updateMoveType(newMoveType: number) {
    this.moveType = newMoveType;
  }

  /**
   * 根据进度计算位置
   * @param progress 0~1
   * @returns
   */
  calcProgress(onePoint: OnePoint, progress: number) : ResultLocation {
    let resultX = 0;
    let resultY = 0;
    let withWaitProgress = Math.min(progress, 1);
    let normalScaleBase = 0.7;
    let scale = normalScaleBase + Math.abs(0.5 - withWaitProgress) * 2 * 0.1;
    switch (this.moveType) {
      case MOVE_LEFT_TO_RIGHT:
        resultX = this.drawRect.left + (this.drawRect.right - this.drawRect.left) * withWaitProgress
        resultY = this.drawRect.top;
        break;
      case MOVE_TOP_TO_BOTTOM:
        resultX = this.drawRect.right;
        resultY = this.drawRect.top + (this.drawRect.bottom - this.drawRect.top) * withWaitProgress
        break;
      case MOVE_RIGHT_TO_LEFT:
        resultX = this.drawRect.right + (this.drawRect.left - this.drawRect.right) * withWaitProgress
        resultY = this.drawRect.bottom;
        break;
      case MOVE_BOTTOM_TO_TOP:
        resultX = this.drawRect.left;
        resultY = this.drawRect.bottom + (this.drawRect.top - this.drawRect.bottom) * withWaitProgress
        break;
      case MOVE_TO_CENTER:
        resultX = onePoint.toCenterStartX + ((this.drawRect.right - this.drawRect.left) / 2 + DEFAULT_RADIUS_SIZE -
        onePoint.toCenterStartX) * withWaitProgress;
        resultY = onePoint.toCenterStartY +
          ((this.drawRect.bottom - this.drawRect.top) / 2 + DEFAULT_RADIUS_SIZE - onePoint.toCenterStartY) *
            withWaitProgress
        scale = normalScaleBase + withWaitProgress * 0.3;
        break;
    }
    return new ResultLocation(resultX, resultY, scale)
  }
}
