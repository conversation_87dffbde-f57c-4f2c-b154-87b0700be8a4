import lottie from '@ohos/lottie';
import { Logger } from '../utils/Logger';

const TAG = 'WaitingViewDot';

@Component
export struct WaitingViewDot {
  private strData: string = "{\"v\":\"5.6.3\",\"fr\":60,\"ip\":0,\"op\":30,\"w\":15,\"h\":15,\"nm\":\"Comp 3\",\"ddd\":0,\"assets\":[],\"layers\":[{\"ddd\":0,\"ind\":1,\"ty\":4,\"nm\":\"dot start 2\",\"sr\":1,\"ks\":{\"o\":{\"a\":0,\"k\":100,\"ix\":11},\"r\":{\"a\":0,\"k\":0,\"ix\":10},\"p\":{\"a\":0,\"k\":[7.5,7.5,0],\"ix\":2},\"a\":{\"a\":0,\"k\":[0,0,0],\"ix\":1},\"s\":{\"a\":0,\"k\":[100,100,100],\"ix\":6}},\"ao\":0,\"shapes\":[{\"d\":1,\"ty\":\"el\",\"s\":{\"a\":0,\"k\":[15,15],\"ix\":2},\"p\":{\"a\":0,\"k\":[0,0],\"ix\":3},\"nm\":\"Ellipse Path 1\",\"mn\":\"ADBE Vector Shape - Ellipse\",\"hd\":false},{\"ty\":\"fl\",\"c\":{\"a\":1,\"k\":[{\"i\":{\"x\":[0.833],\"y\":[0.833]},\"o\":{\"x\":[0.167],\"y\":[0.167]},\"t\":0,\"s\":[1,0.694117665291,0.156862750649,1]},{\"i\":{\"x\":[0.833],\"y\":[0.833]},\"o\":{\"x\":[0.167],\"y\":[0.167]},\"t\":7,\"s\":[1,0.278431385756,0.415686279535,1]},{\"i\":{\"x\":[0.833],\"y\":[0.833]},\"o\":{\"x\":[0.167],\"y\":[0.167]},\"t\":15,\"s\":[0.549019634724,0.278431385756,0.996078431606,1]},{\"i\":{\"x\":[0.833],\"y\":[0.833]},\"o\":{\"x\":[0.167],\"y\":[0.167]},\"t\":22,\"s\":[0.098039217293,0.701960802078,1,1]},{\"t\":30,\"s\":[1,0.694117665291,0.156862750649,1]}],\"ix\":4},\"o\":{\"a\":0,\"k\":100,\"ix\":5},\"r\":1,\"bm\":0,\"nm\":\"Fill 1\",\"mn\":\"ADBE Vector Graphic - Fill\",\"hd\":false}],\"ip\":0,\"op\":106,\"st\":0,\"bm\":0}],\"markers\":[]}";

  private setting: RenderingContextSettings = new RenderingContextSettings(true)
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.setting); // 动效显示
  @Prop
  private pointSize: number = 20;

  private startAnim() {
    let lottieData: object = JSON.parse(this.strData);
    lottie.loadAnimation({
      container: this.context,  // 渲染上下文
      renderer: 'canvas',                          // 渲染方式
      loop: true,                                  // 是否循环播放,默认true
      autoplay: true,                              // 是否自动播放，默认true
      name: TAG,                                // 动画名称
      contentMode: 'Contain',                      // 填充的模式
      frameRate: 30,                               //设置animator的刷帧率为30
      animationData: lottieData,                            // json路径
    })
  }

  build() {
    Stack(){
      Canvas(this.context)
        .width(6)
        .height(6)
        .onAppear(()=> {
          this.startAnim();
        })
        .onReady(()=>{
          //抗锯齿的设置
          this.context.imageSmoothingEnabled = true;
          this.context.imageSmoothingQuality = 'medium'
        })
        .onDisAppear(() => {
          lottie.destroy(TAG);
        })
    }.width(this.pointSize).height(this.pointSize).align(Alignment.Center)

  }
}