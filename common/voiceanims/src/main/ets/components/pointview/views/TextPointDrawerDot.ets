import { Logger } from '../utils/Logger';
import systemDateTime from '@ohos.systemDateTime';
import { BASE_POINT_ANIM_TIME } from '../utils/ConstUtil';

const color1 = '#ffb128'
const color2 = '#7fffb128'
const color3 = '#ff476a'
const color4 = '#7fff476a'
const color5 = '#8c47fe'
const color6 = '#7f8c47fe'
const color7 = '#19b3ff'
const color8 = '#7f19b3ff'
const MAX_SHOW_DURATION = 1500

export const radius = 2;

const TAG = 'TextPointDrawer'

const STORAGE_KEY_IS_DARK_MODE: string = 'VA_IS_DARK_MODE';

const colorArray: Array<string> = [color1, color2,color3,color4,color5,color6,color7,color8]
/**
 * 单个点阵的对象，包含四个点
 */
export class TextPointDrawer {
  // 是否被清除
  cleared: boolean = false;
  row: number = 0;
  column: number = 0;
  // 左边的位置
  xLeft: number = 0;
  top: number = 0
  // 进度 0 -1
  progress: number = 1;

  firstShowTime : number = 0;
  // 从哪个点开始启动
  startPoint: number = 0;
  // 为文本行高
  height: number = 10;
  width: number = 0;
  type: number = 0;
  // 是否显示过
  haveShowed: boolean = false;
  animTime :number = BASE_POINT_ANIM_TIME;

  moveType: number = 0;

  showCanvasWithProgress(drawContext: CanvasRenderingContext2D) {
    let textPoint = this;
    if (textPoint.cleared) {
      Logger.debug(TAG, `showCanvasWithProgress cleared row ${this.row} column ${this.column}`);
      return;
    }
    if (!textPoint.haveShowed) {
      textPoint.firstShowTime = systemDateTime.getTime();
    }
    textPoint.haveShowed = true;
    if (textPoint.firstShowTime !== 0 && systemDateTime.getTime() - textPoint.firstShowTime > MAX_SHOW_DURATION) {
      Logger.debug(TAG, `showCanvasWithProgress show long time row ${this.row} column ${this.column}`);
      return;
    }
    drawContext.save();
    Logger.debug(TAG, `showCanvasWithProgress type ${this.type} row ${this.row} column ${this.column} progress ${textPoint.progress} ${this.xLeft}-${this.top}-${this.xLeft + this.width}-${this.top + this.height}`);
    this.doDrawPoints(drawContext);
    drawContext.restore();
  }

  doDrawPoints(drawContext: CanvasRenderingContext2D) {
    drawContext.save();
    drawContext.beginPath();
    drawContext.arc(this.xLeft + (this.width / 2) , this.top + this.height / 2 , radius , 0, 3.1415 * 2, false);
    drawContext.fillStyle = this.getColor(this.type);
    drawContext.fill();
    drawContext.restore();
  }

  getColor(type: number) : string {
    if(type >= 0 && type < colorArray.length) {
      return colorArray[type]
    }
    return color1;
  }
}

