/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { domain } from './ConstUtil';
import { Logger as Log, LOG } from '@hw-hmf/logger';

const IS_DEBUG_LOG_ENABLE: boolean = false;

/**
 * 日志工具类
 *
 * @since 2024-05-21
 */
export class Logger {
  /**
   * debug日志打印
   *
   * @param tag TAG
   * @param message 待打印的消息
   */
  public static debug(tag: string, message: string): void {
    if (!IS_DEBUG_LOG_ENABLE) {
      return;
    }
    LOG.d(tag, message)
  }

  /**
   * info日志打印
   *
   * @param tag TAG
   * @param message 待打印的消息
   */
  public static info(tag: string, message: string): void {
    LOG.i(tag, message)
  }

  /**
   * warn日志打印
   *
   * @param tag TAG
   * @param message 待打印的消息
   */
  public static warn(tag: string, message: string): void {
    LOG.w(tag, message)
  }

  /**
   * error日志打印
   *
   * @param tag TAG
   * @param message 待打印的消息
   */
  public static error(tag: string, message: string): void {
    LOG.e(tag, message)
  }

  /**
   * fatal日志打印
   *
   * @param tag TAG
   * @param message 待打印的消息
   */
  public static fatal(tag: string, message: string): void {
    LOG.f(tag, message)
  }
}