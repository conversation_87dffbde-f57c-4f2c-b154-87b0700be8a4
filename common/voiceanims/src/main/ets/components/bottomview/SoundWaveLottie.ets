import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import lottie, { AnimationItem } from '@ohos/lottie';
import { SoundWaveLottieController } from './SoundWaveLottieController';

const TAG = 'SoundWaveLottie'

@Component
export struct SoundWaveLottie {
  @Prop soundWaveHeight: Length
  @Prop soundWaveWidth: Length
  @Prop pathData: string = 'pages/lottie/sound_wave.json'
  @Prop scaleSize: number = 1;
  @Prop name: string = TAG;
  private setting: RenderingContextSettings = new RenderingContextSettings(true)
  private context: CanvasRenderingContext2D = new CanvasRenderingContext2D(this.setting); // 动效显示
  controller: SoundWaveLottieController | undefined;
  private animationItem: AnimationItem | undefined = undefined;

  aboutToAppear(): void {
    Logger.info(TAG, 'aboutToAppear');
    if (this.controller) {
      this.controller.startAnimate = this.startAnimate;
      this.controller.pauseAnimate = this.pauseAnimate;
    }
  }

  aboutToDisappear(): void {
    Logger.info(TAG, 'aboutToDisappear');
  }

  /**
   * 启动动画
   */
  private loadAnim() {
    Logger.info(TAG, 'loadAnim');
    this.animationItem = lottie.loadAnimation({
      container: this.context, // 渲染上下文
      renderer: 'canvas', // 渲染方式
      loop: true, // 是否循环播放,默认true
      autoplay: true, // 是否自动播放，默认true,当前设置为false
      name: this.name, // 动画名称
      contentMode: 'Contain', // 填充的模式
      frameRate: 30, //设置animator的刷帧率为30
      path: this.pathData, // json路径
    });
  }

  startAnimate = () => {
    Logger.info(TAG, `startAnimate`);
    // 暂停进程中的所有lottie，注意如果遇到停止问题请排查
    lottie.pause();
    if (!this.animationItem?.isLoaded) { // 如果没有加载
      this.loadAnim();
    }
    lottie.play();
  }

  pauseAnimate = () => {
    Logger.info(TAG, `pauseAnimate`);
    lottie.pause()
  }

  build() {
    Stack() {
      Canvas(this.context)
        .width('100%')
        .height('100%')
        .onReady(() => {
          Logger.info(TAG, 'onReady');
          //抗锯齿的设置
          this.context.imageSmoothingEnabled = true;
          this.context.imageSmoothingQuality = 'medium';
          this.loadAnim();
        })
        .onAppear(()=>{
          Logger.info(TAG, 'onAppear');
        })
        .onDisAppear(() => {
        Logger.info(TAG, 'onDisAppear');
        this.pauseAnimate();
        lottie.destroy(this.name);
      })
    }
    .scale({ x: this.scaleSize, y: this.scaleSize })
    .width(this.soundWaveWidth)
    .height(this.soundWaveHeight)
    .align(Alignment.Center)
  }
}