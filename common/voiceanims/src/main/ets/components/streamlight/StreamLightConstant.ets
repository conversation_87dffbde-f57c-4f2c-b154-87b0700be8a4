export class StreamLightConstant {
  // 渐变颜色
  public static readonly gradualSweepColors: Array<[ResourceColor, number]> =
    [[0xAE42FF,0.0],[0x6F69FF,0.05],[0x179BFF,0.10],
      [0x00E4FF,0.15],[0x00E4FF,0.20],[0x00C4FF,0.25],
      [0x179BFF,0.30],[0x6F69FF,0.35],[0xAE42FF,0.40],
      [0xFF4ED5,0.45],[0xAE42FF,0.50],
      [0x6F69FF,0.55],[0x179BFF,0.60],
      [0x00E4FF,0.65],[0x00E4FF,0.70],[0x00C4FF,0.75],
      [0x179BFF,0.80],[0x6F69FF,0.85],[0xAE42FF,0.90],
      [0xFF4ED5,0.95],[0xAE42FF,1.0]]

  public static readonly gradualLineColors: Array<[ResourceColor, number]> =
    [[0xFF9999, 0.0], [0xFF4ED5, 0.05], [0xAE42FF, 0.10],
      [0x6F69FF, 0.20], [0x179BFF, 0.35], [0x00C4FF, 0.45],
      [0x00FFFA, 0.48], [0x00FFFA, 0.55], [0x4DB3FF, 0.62],
      [0x6784FF, 0.74], [0xDB7BFF, 0.85], [0xFF77EB, 0.95], [0xFF9999, 1.0]]

  public static readonly gradualMaskColors: Array<[ResourceColor, number]> = [['#0000A7FF', 0.0], ['#0000A7FF', 0.5], ['#CC0029D0', 1.0]]

  public static readonly gradualBlurColors: Array<[ResourceColor, number]> = [ ['#1AFFFFFF', 0.0],['#CCFFFFFF', 1.0]]
  // 66 #000000-99 #000000
  public static readonly gradualDarkBlurColors: Array<[ResourceColor, number]> = [['#662C2C2C', 0.0], ['#99434343', 1.0]]
  public static readonly darkBlurColor: ResourceStr = '#991A1A1A'
  public static readonly blurColor: ResourceStr  = '#99FFFFFF'

  public static readonly cancelColor :string = '#14000000'

  public static readonly blurBGColor:string = '#33FFFFFF'
}

export class StreamLightUtils {

  public static transToGradualColor(colorString:string): Array<[ResourceColor, number]>{
    return [[colorString,1.0]]
  }

}