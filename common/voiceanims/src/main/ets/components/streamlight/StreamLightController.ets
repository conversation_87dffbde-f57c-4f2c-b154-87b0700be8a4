export class StreamLightController {
  constructor() {
  }

  /**
   * 开启背景流光动效
   */
  startStreamLightAnimate = (isOpacity?: boolean) => {
  }
  /**
   * 关闭背景流光动效
   */
  stopStreamLightAnimate = () => {
  }
  /**
   * 取消流光效果
   */
  cancelStreamLightAnimate = () => {

  }
  /**
   * 恢复流光效果
   */
  recoveryStreamLightAnimate = () => {

  }
  /**
   * 改变流光效果
   */
  changeStreamLightAnimate = (gradualColor: string | Array<[ResourceColor, number]>) => {

  }

  /**
   * 上滑取消抬手
   */
  onSlideupCancel =  () => {

  }

  /**
   * 用户手势上滑出去
   */
  onSlideupOut = () => {

  }

  /**
   * 用户又重新下滑进入
   */
  onSlideDownIn = () => {

  }
  /**
   * 进入aiAgentCapsule
   */
  enterAiAgentCapsule = () => {

  }
  /**
   * 进入aiAgentCapsule
   */
  enterAiAgentNavBar = () => {

  }
  /**
   * aiAgent跳转Capsule
   */
  aiAgentToCapsule = () => {

  }
  /**
   * aiAgent跳转NavBar
   */
  aiAgentToNavBar = () => {

  }
  /**
   * aiAgent跳转scbBar
   */
  aiAgentCapsuleToExit = (event?: () => void) => {

  }
}