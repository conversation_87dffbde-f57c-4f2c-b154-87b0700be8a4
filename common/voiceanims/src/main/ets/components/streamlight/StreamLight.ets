import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { StreamLightController } from './StreamLightController'
import { displaySync } from '@kit.ArkGraphics2D';
import { StreamLightConstant, StreamLightUtils } from './StreamLightConstant';
import { STORAGE_KEY_IS_DARK_MODE } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';

const TAG = 'StreamLight';

@Component
export struct StreamLight {
  @BuilderParam iconStatusView?: () => void
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) @Watch('watchOfColorMode') isDarkMode: boolean = false;
  /**
   * 起始角度从180开始，否则会反过来了
   */
  @State currentAngle: number = 340;
  controller: StreamLightController | undefined = undefined;
  private backDisplaySync: displaySync.DisplaySync | undefined = undefined;
  private static readonly SHADOW_OFFSET_Y: number = 20
  private extrasHeight: number = StreamLight.SHADOW_OFFSET_Y
  /**
   * 背景的SIZE
   */
  @Prop boxRadius: number;
  @Prop boxHeight: number;
  @Prop @Watch('watchOfColorMode') isAgentBackgroundExist: boolean;
  @State gradualColor: Array<[ResourceColor, number]> = StreamLightConstant.gradualLineColors
  @State isShowBlurBg: boolean = true
  @State alpha: number = 0.15;
  @State color: string = `rgba(0, 0, 0, ${this.alpha})`;
  @State offsetY: number = 16;
  @State radius: number = 15;
  @State gradientLightVisible: Visibility = Visibility.Visible;
  @State blurColor: Array<[ResourceColor, number]> =
    this.isAgentBackgroundExist ? StreamLightConstant.gradualDarkBlurColors :
      (this.isDarkMode ? StreamLightConstant.gradualDarkBlurColors : StreamLightConstant.gradualBlurColors)
  isAgentShow: boolean = false
  @State blurRadius: number = 0;
  @State lightDegree: number = 0;
  /**
   * 描边的宽度 1vp
   */
  private readonly STROKE_WIDTH = 1;

  private draw30 = (intervalInfo: displaySync.IntervalInfo) => { // 订阅回调函数，字体大小在25到150之间变化
  if (this.currentAngle < 360) {
    this.currentAngle += 2
  } else {
    this.currentAngle = 0
  }
};

  aboutToAppear(): void {
    Logger.info(TAG, 'aboutToAppear')
    this.extrasHeight = this.isAgentShow ? 0 : StreamLight.SHADOW_OFFSET_Y
    this.blurRadius = 5
    this.lightDegree = this.isAgentBackgroundExist ? -0.1 : (this.isDarkMode ? -0.1 : 0.5)
    if (this.controller) {
      this.controller!.startStreamLightAnimate = this.start;
      this.controller!.stopStreamLightAnimate = this.stop;
      this.controller!.cancelStreamLightAnimate = this.cancel;
      this.controller!.changeStreamLightAnimate = this.change;
      this.controller!.recoveryStreamLightAnimate = this.recovery;
      this.controller.onSlideupOut = this.onSlideupOut.bind(this);
      this.controller.onSlideDownIn = this.onSlideDownIn.bind(this);
      this.controller.onSlideupCancel = this.onSlideupCancel.bind(this);
    }
    Logger.info(TAG, 'isAgentBackgroundExist', this.isAgentBackgroundExist);
  }

  aboutToDisappear(): void {
    this.backDisplaySync?.off('frame', this.draw30);
  }

  watchOfColorMode() {
    if (this.isDarkMode || this.isAgentBackgroundExist) {
      this.blurColor = StreamLightConstant.gradualDarkBlurColors
      this.blurRadius = 5
      this.lightDegree = -0.1
    } else {
      this.blurColor = StreamLightConstant.gradualBlurColors
      this.blurRadius = 5
      this.lightDegree = 0.5
    }
  }

  createDisplaySyncSlow() {
    let range: ExpectedFrameRateRange = {
      // 创建和配置帧率参数
      expected: 30, // 设置期望绘制帧率为30hz
      min: 0, // 配置帧率范围
      max: 120 // 配置帧率范围
    };

    this.backDisplaySync = displaySync.create(); // 创建DisplaySync实例
    this.backDisplaySync.setExpectedFrameRateRange(range); // 设置帧率
    this.backDisplaySync.on('frame', this.draw30); // 订阅frame事件和注册订阅函数
  }

  /**
   * 启动流光旋转动效
   */
  start = () => {
    Logger.info(TAG, 'StreamLight start');
    if (this.backDisplaySync === undefined) {
      this.createDisplaySyncSlow();
    }
    this.backDisplaySync?.start();
  }
  /**
   * 停止流光旋转动效
   */
  stop = () => {
    Logger.info(TAG, 'StreamLight stop');
    this.backDisplaySync?.stop();
  }
  /**
   * 上滑变成灰色
   */
  cancel = () => {
    this.change(StreamLightConstant.cancelColor);
    this.isShowBlurBg = false;
  }
  recovery = () => {
    this.change(StreamLightConstant.gradualLineColors)
    this.isShowBlurBg = true;
    this.start()
  }
  /**
   * 改变流光的背景
   */
  change = (gradualColor: string | Array<[ResourceColor, number]>) => {
    Logger.info(TAG, `StreamLight change ${gradualColor}`);
    this.stop();
    this.backDisplaySync = undefined
    if (typeof (gradualColor) === 'string') {
      this.gradualColor = StreamLightUtils.transToGradualColor(gradualColor)
    } else {
      this.gradualColor = gradualColor as Array<[ResourceColor, number]>
    }
  }

  private onSlideupOut(): void {
  }

  private onSlideDownIn(): void {
    // 设置流光可见
    this.gradientLightVisible = Visibility.Visible;
    // 流光启动
    this.start();
  }

  private onSlideupCancel(): void {
    // 仅设置流光可见
    this.gradientLightVisible = Visibility.Visible;
  }

  build() {
    Stack({ alignContent: Alignment.Center }) {

      // 内阴影
      Stack() {
        Column()
          .backgroundColor(Color.White)
          .backgroundBrightness({
            rate: 0.5,
            lightUpDegree: this.lightDegree
          })
          .width('calc(100% - 4vp)')
          .height(this.boxHeight - 4)
          .borderRadius(this.boxRadius - 2)
        Column()
          .backgroundColor(Color.White)
          .width('calc(100% - 8vp)')
          .height(this.boxHeight - 8)
          .borderRadius(this.boxRadius - 4)
          .blendMode(BlendMode.CLEAR)
      }
      .id('stream_light_inner_shadow')
      .clip(true)
      .visibility(this.gradientLightVisible)
      .blendMode(BlendMode.SRC_ATOP, BlendApplyType.OFFSCREEN)


      // 上叠层
      Column() {
      }
      .id('stream_light_blur')
      .linearGradient({
        angle: 180,
        colors: this.blurColor
      })
      .backgroundEffect({
        radius: this.blurRadius,
        saturation: 1,
        brightness: 1,
      })
      .width('calc(100% - 2vp)')
      .height(this.boxHeight - this.STROKE_WIDTH * 2)
      .borderRadius(this.boxRadius)
      .visibility(this.isShowBlurBg ? Visibility.Visible : Visibility.Hidden)

      // 渐变层
      Stack({ alignContent: Alignment.Center }) {
        // 宽高
        Column() {
        }
        .width('100%')
        .height(this.boxHeight)
        .borderRadius(this.boxRadius)
        .backgroundColor(Color.White)
        .shadow({
          radius: this.radius,
          color: this.color,
          offsetY: this.offsetY,
          fill: true
        })

        Column()
          .backgroundColor(Color.White)
          .width('calc(100% - 2vp)')
          .height(this.boxHeight - this.STROKE_WIDTH * 2)
          .borderRadius(this.boxRadius)
          .blendMode(BlendMode.CLEAR)
          .offset({ y: this.isNeedOffset() ? '0.8px' : '0' })

        this.iconStatusView?.()

        Column() {
        }
        .hitTestBehavior(HitTestMode.Transparent)
        .width('100%')
        .height(this.boxHeight + (StreamLight.SHADOW_OFFSET_Y * 2))
        .linearGradient({
          angle: this.currentAngle,
          colors: this.gradualColor
        })
        .id('stream_light_gradient_mask')
        .blendMode(BlendMode.SRC_IN)
      }
      .id('stream_light_gradient')
      .clip(true)
      .visibility(this.gradientLightVisible)
      .blendMode(BlendMode.SRC_OVER, BlendApplyType.OFFSCREEN)

    }
    .id(TAG)
    .width('100%')
    .height(this.boxHeight + (this.extrasHeight * 2))
    .margin({ top: -1 * this.extrasHeight })
  }

  private isNeedOffset(): boolean {
    let isNeed: boolean =
      (Math.round(vp2px(this.boxHeight)) - Math.round(vp2px(this.boxHeight - this.STROKE_WIDTH * 2))) % 2 !== 0;
    Logger.info(TAG, 'isNeedOffset ' + isNeed);
    return isNeed;
  }
}
