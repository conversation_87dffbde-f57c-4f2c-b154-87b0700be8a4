import { StreamLightController } from './StreamLightController'
import lazy { default as displaySync } from '@ohos.graphics.displaySync';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger'
import { PanelState } from '@hms-assistant/common-corebase/src/main/ets/constant/Constants'

const TAG = 'HalfStreamLight'

@Component
export struct HalfStreamLight {
  controller: StreamLightController | undefined = undefined;
  private backDisplaySync: displaySync.DisplaySync | undefined = undefined;
  @Prop boxHeight: number;
  @Prop boxWidth: number;
  @Prop boxRadius: number | Length;
  @Prop bottomMarginSize: number;
  @Prop panelStatus: PanelState;
  // shadow
  @State shadowRadius: number = 50;
  @State shadowColor: ResourceStr | Resource | Color = 'rgba(0,0,0,0.3)';
  @State shadowOffsetY: number = 20;
  // gradient
  @State currentRotation: number = 0;
  @State centerY: Length = '50%';
  @State boxOpacity: number = 1;

  private draw30 = (intervalInfo: displaySync.IntervalInfo) => { // 订阅回调函数，字体大小在25到150之间变化
  if (this.currentRotation < 360) {
    this.currentRotation += 2;
  } else {
    this.currentRotation = 0;
  }
};

  aboutToAppear(): void {
    Logger.info(TAG, 'aboutToAppear');
    if (this.panelStatus === PanelState.NAV_BAR_COLOR||this.panelStatus === PanelState.SCB_BAR) {
      this.shadowRadius = 0;
      this.centerY = '70%';
      if (this.backDisplaySync === undefined) {
        this.createDisplaySyncSlow();
      }
      this.backDisplaySync?.start();
    }
    if (this.controller) {
      this.controller.enterAiAgentCapsule = this.enterAiAgentCapsule;
      this.controller.enterAiAgentNavBar = this.enterAiAgentNavBar;
      this.controller.aiAgentToCapsule = this.aiAgentToCapsule;
      this.controller.aiAgentToNavBar = this.aiAgentToNavBar;
      this.controller.aiAgentCapsuleToExit = this.exitAiAgent;
    }
  }

  aboutToDisappear(): void {
    this.backDisplaySync?.stop();
    this.backDisplaySync?.off('frame', this.draw30);
    if (this.backDisplaySync !== undefined) {
      this.backDisplaySync = undefined;
    }
  }

  createDisplaySyncSlow() {
    let range: ExpectedFrameRateRange = {
      // 创建和配置帧率参数
      expected: 60, // 设置期望绘制帧率为30hz
      min: 0, // 配置帧率范围
      max: 120 // 配置帧率范围
    };

    this.backDisplaySync = displaySync.create(); // 创建DisplaySync实例
    this.backDisplaySync.setExpectedFrameRateRange(range); // 设置帧率
    this.backDisplaySync.on('frame', this.draw30); // 订阅frame事件和注册订阅函数
  }

  enterAiAgentCapsule = () => {
    if (this.backDisplaySync === undefined) {
      this.createDisplaySyncSlow();
    }
    this.backDisplaySync?.start();
  }
  enterAiAgentNavBar = () => {
    Logger.info(TAG, 'enterAiAgentNavBar');
    animateTo({
      duration: 150,
      curve: Curve.Sharp,
    }, () => {
      this.shadowRadius = 0;
      this.centerY = '70%';
    })
    if (this.backDisplaySync === undefined) {
      this.createDisplaySyncSlow();
    }
    this.backDisplaySync?.start();
  }
  aiAgentToCapsule = () => {
    Logger.info(TAG, 'aiAgentToCapsule');
    animateTo({
      duration: 150,
      curve: Curve.Sharp,
    }, () => {
      this.shadowRadius = 50;
      this.centerY = '50%';
    })

  }
  aiAgentToNavBar = () => {
    Logger.info(TAG, 'aiAgentToNavBar');
    animateTo({
      duration: 150,
      curve: Curve.Sharp,
    }, () => {
      this.shadowRadius = 0;
      this.centerY = '70%';
    })
  }
  exitAiAgent = (event?: () => void) => {
    Logger.info(TAG, 'exitAiAgent');
    animateTo({
      duration: 150,
      curve: Curve.Sharp,
      onFinish: () => {
        this.backDisplaySync?.stop();
        this.backDisplaySync = undefined;
        event?.();
      }
    }, () => {
      this.boxOpacity = 0;
    })

  }

  build() {
    // 渐变层
    Stack({ alignContent: Alignment.Center }) {
      Column()
        .width(this.boxWidth)
        .height(this.boxHeight)
        .borderRadius(this.boxRadius)
        .backgroundColor(Color.White)
        .shadow({
          radius: this.shadowRadius,
          color: this.shadowColor,
          offsetY: this.shadowOffsetY,
          fill: true
        })
      Column()
        .width(this.boxWidth + 100)
        .height(this.boxHeight + 100)
        .sweepGradient({
          center: ['50%', this.centerY],
          start: 0,
          end: 360,
          rotation: this.currentRotation,
          colors:
          [[0xAE42FF, 0.0], [0x6F69FF, 0.1], [0x179BFF, 0.2],
            [0x00E4FF, 0.3], [0x00E4FF, 0.4], [0x00C4FF, 0.5],
            [0x179BFF, 0.6], [0x6F69FF, 0.7], [0xAE42FF, 0.8],
            [0xFF4ED5, 0.9], [0xAE42FF, 1.0]]
        })
        .blendMode(BlendMode.SRC_IN)
    }
    .blendMode(BlendMode.SRC_OVER, BlendApplyType.OFFSCREEN)
    .id(TAG)
    .opacity(this.boxOpacity)
    .transition(TransitionEffect.asymmetric(TransitionEffect.OPACITY.animation({ duration: 150, curve: Curve.Sharp }),
      TransitionEffect.OPACITY.animation({ duration: 250, curve: Curve.Sharp })))
    .width(this.boxWidth + (this.bottomMarginSize * 2))
    .height(this.boxHeight + (this.bottomMarginSize * 2))
  }
}