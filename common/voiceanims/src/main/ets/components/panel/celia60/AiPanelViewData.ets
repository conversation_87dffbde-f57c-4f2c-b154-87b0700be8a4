/**
 * 6.0AiPanel数据体
 */
@Observed
export class AiPanelViewData {
  private static mInstance: AiPanelViewData;
  /**
   * 展示的面板卡片宽度
   * 占据栅格化100%
   */
  public displayCardWidth: Length = '100%';
  /**
   * 展示的面板卡片高度
   */
  public displayCardHeight: Length = 0;

  /**
   * 面板内容位移，通过位移来显示内容区域
   */
  public contentTranslateY: number = 0;

  /**
   * 底部view的高度, 默认为自适应 由其底部区域的高度决定;
   * 动画退场时，设置其固定高度，其余场景自适应
   */
  public bottomViewHeight: Length | null = null;

  /**
   * 面板透明度(整个小艺panel透明度)
   */
  public panelOpacity: number = 1;
  /**
   * 小艺窗口透明度(卡片)
   */
  public displayCardOpacity: number = 1
  /**
   * 模拟导航条透明度
   */
  public mockSystemBarOpacity: number = 0;
  /**
   * 内容透明度
   */
  public contentOpacity: number = 0;
  /**
   * 拖拽条透明度(标题栏透明度)
   */
  public dragBarOpacity: number = 0;

  /**
   * 底部view的透明度
   */
  public bottomViewOpacity: number = 1;

  /**
   * 虚拟的导航条
   */
  public isMockSystemBarShow: boolean = false;

  /**
   * 屏幕的宽度
   */
  public windowWidth:number = 0;

  /**
   * 屏幕的高度
   */
  public windowHeight:number = 0;

  /**
   * 卡片底部透明度
   */
  public cardBottomViewOpacity: number = 1;
  /**
   * 阴影模糊半径
   */
  public shadowRadius: number = 60;
  /**
   * 阴影颜色
   */
  public shadowColor: string = '#1A000000';

  static getInstance(): AiPanelViewData {
    if (!AiPanelViewData.mInstance) {
      AiPanelViewData.mInstance = new AiPanelViewData();
    }
    return AiPanelViewData.mInstance;
  }

  reset(): void {
    this.displayCardWidth = '100%';
    this.displayCardHeight = 0;
    this.contentTranslateY = 0;
    this.bottomViewHeight = null;
    this.panelOpacity = 1;
    this.displayCardOpacity = 1;
    this.mockSystemBarOpacity = 0;
    this.contentOpacity = 0;
    this.dragBarOpacity = 0;
    this.bottomViewOpacity = 1;
    this.cardBottomViewOpacity = 1;
    this.shadowRadius = 60;
    this.shadowColor = '#1A000000';
  }
}