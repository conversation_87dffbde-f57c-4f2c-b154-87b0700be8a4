import rpc from '@ohos.rpc';
import { Logger } from '../pointview/utils/Logger';
import { SafeJson } from '@hms-security/agoh-base-sdk'
import { DragResultOriData } from '@hms-assistant/common-corebase/src/main/ets/bean/ScbBarBean';

const TAG = 'LongTakeAnimManager';

const EMPTY_STR = '';

/**
 * 一镜到底管理类
 */
export class LongTakeAnimManager {
  private static sInstance: LongTakeAnimManager | undefined = undefined;

  /**
   * 通知桌面条小艺面板展示
   * 该值和桌面绑定，不可变更
   */
  private readonly NOTIFY_SCB_BAR_AI_WINDOW_SHOWN = 1003;

  /**
   * 通知桌面条断开连接
   */
  private readonly NOTIFY_SCB_BAR_DISCONNECT = 1004;

  /**
   * 通知桌面条显示
   */
  private readonly NOTIFY_SCB_BAR_SHOW = 1007;

  /**
   * 通知桌面条小艺进入伴随态
   */
  private readonly NOTIFY_SCB_BAR_ENTER_ACCOMPANIED = 1015;

  /**
   * 通知桌面条小艺退出伴随态
   */
  private readonly NOTIFY_SCB_BAR_QUIT_ACCOMPANIED = 1016;

  /**
   * AI导航条长按150ms开始放大，执行到650ms时运动到可以衔接的大小，
   * 而条是200ms才拉的小艺，所以小艺需在(650 - 200)的时间点去响应
   */
  private readonly SHOW_AI_WINDOW_MIN_DURATION = 450;

  /**
   * 桌面导航条ipc代理
   */
  private scbBarProxy: rpc.IRemoteObject | undefined = undefined;

  /**
   * 长按条200ms时间点
   */
  private longPressBar200Time: number = 0;

  /**
   * 是否需要展示AI窗口
   * 条会在500ms时，通知小艺展示，如若未收到认为不展示
   */
  private isShowAiWindow: boolean = false;

  /**
   * 是否一镜到底
   * 认为桌面导航条启动半屏态窗口为true
   */
  private isLongTakeWindow: boolean = false;

  private processCodeStartMillion: number = 0;

  /**
   * 是否在拖拽中
   */
  private isInDragNow: boolean = false;

  /**
   * 页面回调
   */
  private pageCallBack: LongTakeAnimCallBack | undefined = undefined;

  /**
   * 是否收到init
   */
  private isReceiveInit: boolean = false;

  /**
   * 是否立即展示AI窗口
   * 无需等待导航条通知，初始化后可立即渲染
   */
  private isImmediatelyShowAiWindow: boolean = false;

  /**
   * 缓存的show方法
   * IPC通信不能保证时序性，如果无序了，应当缓存show方法
   */
  public cachedShowMethod = () => {

  };

  /**
   * 缓存拖拽dragShow方法
   * IPC通信不能保证时序性，如果无序了，应当缓存dragShow方法
   */
  public cachedDragShowMethod = () => {

  }

  private cachedDrag: DragResultOriData | undefined = undefined;

  private constructor() {
  }

  /**
   * 单例
   *
   * @returns 对象
   */
  public static getInstance(): LongTakeAnimManager {
    if (LongTakeAnimManager.sInstance === undefined) {
      LongTakeAnimManager.sInstance = new LongTakeAnimManager();
    }
    return LongTakeAnimManager.sInstance;
  }

  /**
   * 保存用于binder通信的句柄
   *
   * @param proxy 代理对象
   */
  public cachedScbProxy(proxy: rpc.IRemoteObject) {
    Logger.info(TAG, 'cachedScbProxy');
    this.isReceiveInit = true;
    this.scbBarProxy = proxy;
  }

  public isHasReceiveInit(): boolean {
    return this.isReceiveInit;
  }

  /**
   * 桌面导航条通知窗口预加载
   *
   * @param jsonStr 通信Json格式字符串
   * @param callBack 回调处理
   */
  public aiWindowInit(jsonStr: string, callBack: LongTakeAnimCallBack, showImmediately: boolean): void {
    Logger.info(TAG, `aiWindowInit ${jsonStr}`);
    if (showImmediately) {
      // 无需计算执行时间，完成初始化后可立即执行
      Logger.info(TAG, 'receive immediately show signal during init');
      this.isImmediatelyShowAiWindow = true;
      callBack.aiWindowInit?.();
      return;
    }
    this.isLongTakeWindow = true;
    this.processCodeStartMillion = new Date().getTime();
    this.longPressBar200Time = this.parseStartTime(jsonStr);
    callBack.aiWindowInit?.();
    this.cachedShowMethod();
    this.cachedShowMethod = () => {
    }
  }

  /**
   * 是否200ms内二次唤醒
   *
   * @returns 是否是快速唤醒
   */
  public isFastTwiceWakeup(jsonStr: string): boolean {
    if (this.longPressBar200Time === 0) {
      return false;
    }

    let newWakeupTime: number = this.parseStartTime(jsonStr);
    if (newWakeupTime - this.longPressBar200Time < 200) {
      Logger.warn(TAG, 'isFastTwiceWakeup ignore.');
      return true;
    }
    return false;
  }

  private parseStartTime(jsonStr: string): number {
    let jsonObject: Record<string, object | number> | null = SafeJson.ohAegJsonParse(jsonStr);
    if (jsonObject === null || jsonObject.startTime === undefined) {
      Logger.warn(TAG, 'parseStartTime time is null.');
      return new Date().getTime();
    }

    if (typeof jsonObject.startTime === 'string') {
      return parseInt(jsonObject.startTime);
    } else {
      return jsonObject.startTime as number;
    }
  }

  /**
   * 通知AI窗口展示
   *
   * @param proxy 与条通信句柄
   */
  public aiWindowShow(): void {
    Logger.info(TAG, 'aiWindowShow');
    this.isShowAiWindow = true;
  }

  /**
   * 判断当前导航条唤醒运行中
   *
   * @returns true
   */
  public isAiBarWakeupOk(): boolean {
    return this.isShowAiWindow && this.isReceiveInit;
  }

  public aiWindowInitDrag(callBack: LongTakeAnimCallBack): void {
    this.isLongTakeWindow = true;
    this.isInDragNow = true;
    // 通知窗口预加载
    callBack.aiWindowInit?.();
    this.cachedDragShowMethod();
    this.cachedDragShowMethod = () => {
    }
  }

  public aiWindowDragShow(dragData: DragResultOriData): void {
    Logger.debug(TAG, `aiWindowDragShow ${dragData}`);
    Logger.info(TAG, `aiWindowDragShow pageCallBack ${this.pageCallBack === undefined}`);
    // DTS2024060322484 概率出现callback设置在拖拽展示之后
    if (this.pageCallBack === undefined) {
      this.cachedDrag = dragData;
    } else {
      // 执行完拖拽展示需要重置拖拽中的状态
      this.isInDragNow = false;
      this.pageCallBack?.aiWindowAnimDragShow?.(dragData);
      this.pageCallBack?.aiWindowTouchable?.();
    }
  }

  /**
   * rpc通知导航条AI窗口展示
   */
  public notifyScbBarAiWindowShown(): void {
    Logger.info(TAG, 'notifyScbBarAiWindowShown');
    if (this.scbBarProxy === undefined) {
      return;
    }
    let data = rpc.MessageSequence.create();
    let reply = rpc.MessageSequence.create();
    let option = new rpc.MessageOption();
    try {
      this.scbBarProxy.sendMessageRequest(this.NOTIFY_SCB_BAR_AI_WINDOW_SHOWN, data, reply, option);
      Logger.info(TAG, 'sendMessageRequest');
      reply.readException();
    } catch (error) {
      Logger.info(TAG, `rpc error ${SafeJson.ohAegJsonStringify(error)}`);
    } finally {
      data.reclaim();
      reply.reclaim();
    }
  }

  /**
   * 是否是一镜到底
   *
   * @returns true 一镜到底
   */
  public isLongTakeAnim(): boolean {
    return this.isLongTakeWindow;
  }

  /**
   * 判断当前是否处于拖拽中
   *
   * @returns true 处于拖拽过程中，拖拽没有结束时该状态为true
   */
  public isInDrag(): boolean {
    return this.isInDragNow;
  }

  /**
   * 展示时检查是否当前处于拖拽状态
   *
   * @returns
   */
  public isWakeupShowCheckHasDrag(): boolean {
    if (!this.isInDragNow) {
      return false;
    }
    Logger.warn(TAG, 'execute isWakeupShowCheckHasDrag');
    if (this.pageCallBack) {
      this.pageCallBack.aiWindowAnimShow?.();
      this.pageCallBack.aiWindowTouchable?.();
    }
    this.isInDragNow = false;
    return true;
  }

  /**
   * rpc通知导航条AI窗口断开连接
   */
  public notifyScbBarDisconnect(): void {
    Logger.info(TAG, 'notifyScbBarDisconnect');
    if (this.scbBarProxy === undefined) {
      return;
    }
    let data = rpc.MessageSequence.create();
    let reply = rpc.MessageSequence.create();
    let option = new rpc.MessageOption();
    try {
      this.scbBarProxy.sendMessageRequest(this.NOTIFY_SCB_BAR_DISCONNECT, data, reply, option);
      Logger.info(TAG, `sendMessageRequest`);
      reply.readException();
    } catch (error) {
      Logger.info(TAG, `rpc error ${SafeJson.ohAegJsonStringify(error)}`);
    } finally {
      data.reclaim();
      reply.reclaim();
    }
    this.release();
  }

  /**
   * rpc通知导航条小艺伴随态的改变事件；当处于伴随态时，导航条会防止拖拽断连。
   */
  public notifyScbBarAgentAccompanied(isAccompanied: boolean): void {
    Logger.info(TAG, `notifyScbBarAgentAccompanied: ${isAccompanied}`);
    if (this.scbBarProxy === undefined) {
      return;
    }
    let data = rpc.MessageSequence.create();
    let reply = rpc.MessageSequence.create();
    let option = new rpc.MessageOption();
    try {
      this.scbBarProxy.sendMessageRequest(isAccompanied ? this.NOTIFY_SCB_BAR_ENTER_ACCOMPANIED :
      this.NOTIFY_SCB_BAR_QUIT_ACCOMPANIED, data, reply, option);
      Logger.info(TAG, `sendMessageRequest`);
      reply.readException();
    } catch (error) {
      Logger.info(TAG, `rpc error ${SafeJson.ohAegJsonStringify(error)}`);
    } finally {
      data.reclaim();
      reply.reclaim();
    }
  }

  /**
   * 执行窗口展示
   */
  public actionShowWindow(callBack: LongTakeAnimCallBack): void {
    // 导航条无动画，无需接续可直接展示
    if (this.isImmediatelyShowAiWindow) {
      Logger.info(TAG, 'actionShowWindow from immediately');
      callBack.aiWindowAnimShow?.(this.isImmediatelyShowAiWindow);
      callBack.aiWindowTouchable?.();
      return;
    }

    // 非一镜到底，说明是普通拉起直接展示即可
    if (!this.isLongTakeWindow) {
      Logger.info(TAG, 'actionShowWindow no longTakeWindow.');
      callBack.aiWindowAnimShow?.();
      return;
    }

    // 判断是拖拽触发
    if (this.isInDragNow) {
      Logger.info(TAG, `is in dragging  isCacheDrag ${this.cachedDrag === undefined}`);
      // 已约定回调句柄在init时返回
      this.pageCallBack = callBack;
      // UI回调并不代表一定收到show消息，所以此并不能展示窗口
      if (this.cachedDrag !== undefined) {
        // 执行完拖拽展示需要重置拖拽中的状态
        this.isInDragNow = false;
        this.pageCallBack?.aiWindowAnimDragShow?.(this.cachedDrag);
        this.pageCallBack?.aiWindowTouchable?.();
      }
      return;
    }

    // 窗口预加载时间点
    let windowInitMillions: number = new Date().getTime();
    let distanceMillions: number = windowInitMillions - this.longPressBar200Time;
    Logger.info(TAG, `actionShowWindow isShowAiWindow = ${this.isShowAiWindow}, distance200 = ${distanceMillions}`);

    if (this.processCodeStartMillion !== 0) {
      let fromPressToReceiveInit: number = this.processCodeStartMillion - this.longPressBar200Time;
      let fromInitToFirstFrame: number = windowInitMillions - this.processCodeStartMillion;
      Logger.info(TAG, `fromPressToReceiveInit ${fromPressToReceiveInit}, fromInitToFirstFrame ${fromInitToFirstFrame}`);
    }

    // 一镜到底时，还没收到展示的通知(情况1:用户在500ms前撤销了长按; 情况2:窗口加载很快, 关注热启动)
    if (!this.isShowAiWindow) {
      Logger.warn(TAG, 'actionShowWindow no receive show code！');
      // 如果650ms以上都没收到show，说明用户撤销了长按
      if (distanceMillions >= this.SHOW_AI_WINDOW_MIN_DURATION) {
        // 取消建立连接，并销毁窗口
        // 条主动断连
        callBack.aiWindowDestroy?.();
        Logger.warn(TAG, 'disconnect here');
        return;
      }

      let timeNextDuration: number = this.SHOW_AI_WINDOW_MIN_DURATION - distanceMillions;
      // 延迟第650ms时再检查标记位置
      Logger.warn(TAG, `setTimeOut ${timeNextDuration}`);
      setTimeout(() => {
        Logger.warn(TAG, `timeOutRunning. isShowAiWindow ${this.isShowAiWindow}`);
        if (this.isShowAiWindow) {
          callBack.aiWindowAnimShow?.();
          callBack.aiWindowTouchable?.();
        } else {
          // 取消建立连接，并销毁窗口
          // 条主动断连
          callBack.aiWindowDestroy?.();
          Logger.warn(TAG, 'disconnect here');
        }
      }, timeNextDuration);
      return;
    }

    // 收到长按500ms消息，且预加载时延>450ms，立即展示; 否则延迟展示
    if (distanceMillions >= this.SHOW_AI_WINDOW_MIN_DURATION) {
      callBack.aiWindowAnimShow?.();
      callBack.aiWindowTouchable?.();
    } else {
      setTimeout(() => {
        callBack.aiWindowAnimShow?.();
        callBack.aiWindowTouchable?.();
      }, this.SHOW_AI_WINDOW_MIN_DURATION - distanceMillions);
    }
  }

  /**
   * 通知导航条展示
   */
  public notifyScbBarShow(): void {
    Logger.info(TAG, 'notifyScbBarShow');
    if (this.scbBarProxy === undefined) {
      return;
    }
    this.isInDragNow = false;
    this.isShowAiWindow = false;
    this.isImmediatelyShowAiWindow = false;
    this.longPressBar200Time = 0;
    let data = rpc.MessageSequence.create();
    let reply = rpc.MessageSequence.create();
    let option = new rpc.MessageOption();
    try {
      this.scbBarProxy.sendMessageRequest(this.NOTIFY_SCB_BAR_SHOW, data, reply, option);
      Logger.info(TAG, `sendMessageRequest`);
      reply.readException();
    } catch (error) {
      Logger.info(TAG, `rpc error ${SafeJson.ohAegJsonStringify(error)}`);
    } finally {
      data.reclaim();
      reply.reclaim();
    }
  }

  /**
   * 除了通信句柄scbBarProxy其余全部清理
   * 用于小艺半屏态退出时
   */
  public releaseExceptProxy(): void {
    Logger.info(TAG, 'halfPageExit');
    this.isShowAiWindow = false;
    this.isImmediatelyShowAiWindow = false;
    this.longPressBar200Time = 0;
    this.isLongTakeWindow = false;
    this.processCodeStartMillion = 0;
    this.isInDragNow = false;
    this.pageCallBack = undefined;
    this.isReceiveInit = false;
    this.cachedDrag = undefined;
    this.cachedShowMethod = () => {
    };
    this.cachedDragShowMethod = () => {
    };
  }

  /**
   * 释放
   */
  public release() {
    Logger.info(TAG, 'release');
    this.isShowAiWindow = false;
    this.isImmediatelyShowAiWindow = false;
    this.longPressBar200Time = 0;
    this.scbBarProxy = undefined;
    this.isLongTakeWindow = false;
    this.processCodeStartMillion = 0;
    this.isInDragNow = false;
    this.pageCallBack = undefined;
    this.isReceiveInit = false;
    this.cachedDrag = undefined;
    this.cachedShowMethod = () => {
    };
    this.cachedDragShowMethod = () => {
    };
  }
}

/**
 * 一镜到底方法回调
 */
export class LongTakeAnimCallBack {
  /**
   * 半屏态窗口预加载
   */
  aiWindowInit? = () => {

  }

  /**
   * 半屏态窗口动画展示
   */
  aiWindowAnimShow? = (isImmediatelyShow?: boolean) => {

  }

  /**
   * 设置窗口可触摸
   */
  aiWindowTouchable? = () => {

  }

  /**
   * 窗口销毁
   */
  aiWindowDestroy? = () => {

  }

  /**
   * 半屏态窗口拖拽展示
   */
  aiWindowAnimDragShow? = (jsonStr: DragResultOriData) => {

  }
}