/**
 * agent 动效参数
 */
import { curves } from '@kit.ArkUI'

export class AgentAnimateConst {
  public static readonly agentZero: number = 0

  // 渐变颜色
  public static readonly gradualColors: string[] = ['#e2e5f6', '#e0e3f4', '#dcddf1','#e0dcf1','#e6ddf1']

  /**
   * 胶囊态
   */

  public static readonly capsuleBarWidth: number = 264
  public static readonly capsuleBarHeight: number = 48
  public static readonly capsuleBorderRadius: number = 25
  public static readonly capsuleBallScale: number = 1
  public static readonly capsuleBallSize: number = 50
  public static readonly capsuleBarResultWidth: number = 126
  public static readonly capsuleBarClarifyWidth: number = 296
  public static readonly capsuleBarContentWidth: number = 198
  public static readonly capsuleShadowRadius: number = 8
  public static readonly capsuleShadowOffsetY: number = 4
  public static readonly capsuleVoiceRegionWidth: number = 42 // 球+左margin

  /**
   * 彩条态
   */
  public static readonly navBarWidth: number = 112
  public static readonly navbarHeight: number = 5
  public static readonly navbarHotHeight: number = 32
  public static readonly navbarBorderRadius: number = 32
  public static readonly navbarShadowColor: string = '#00000000'
  public static readonly barMargin: number = 45

  /**
   * （彩条热区宽度 - 彩条宽度）/ 2
   */
  public static readonly navBarHotMargin: number = 10


  /**
   * agent动效曲线
   */
  public static readonly agentCurve:ICurve = curves.interpolatingSpring(0, 1, 228, 24)
  public static readonly heightCurve:ICurve = curves.interpolatingSpring(0, 1, 228, 30)
  public static readonly effectCurve:ICurve = curves.cubicBezierCurve(0.2, 0, 0.37, 1)
}