/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import {
  AbilityContextProviderListener,
  BaseRecognizeListener,
  BaseUiListener,
  CreateParams,
  InitRecognizeParams,
  MultimodalSdkCreator,
  PostMessageParams,
  SdkConfig,
  StartRecognizeParams,
  StopBusinessParams,
  TtsParams,
  VoiceKitCallback
} from '@hms-assistant/common-aikit';
import { AiFrameConfig, KitConstant, MultimodalContext, MultimodalEvent } from '@hms-assistant/common-aikitbase';
import { MultimodalSdk } from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/MultimodalSdk';
import common from '@ohos.app.ability.common';
import { KitDemoLog } from '../base/KitDemoLogger';
import { SessionChangeListener } from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/SessionChangeListener';
import { BaseTtsListener } from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/tts/BaseTtsListener';
import { RenewSessionParams } from '@hms-assistant/common-aikit/src/main/ets/base/bean/params/RenewSessionParams';

const TAG: string = 'KitSdkConnector'

export class KitSdkConnector {
  private static instance: KitSdkConnector = new KitSdkConnector();
  private sdkImpl?: MultimodalSdk;
  private context?: common.ApplicationContext;
  private uiAbilityContext?: common.UIAbilityContext;


  private constructor() {
  }

  public static getInstance(): KitSdkConnector {
    return KitSdkConnector.instance;
  }

  public setSdkImpl(impl: MultimodalSdk) {
    this.sdkImpl = impl;
  }

  public setAppContext(context: common.ApplicationContext) {
    this.context = context;
  }

  public setUiAbilityContext(context: common.UIAbilityContext) {
    this.uiAbilityContext = context;
  }

  public getAppContext(): common.ApplicationContext | undefined {
    return this.context;
  }

  public getUiAbilityContext(): common.UIAbilityContext | undefined {
    return this.uiAbilityContext;
  }

  public async create(aiframeConfig: AiFrameConfig, params: CreateParams, sdkConfig: SdkConfig,
    listener: SessionChangeListener): Promise<MultimodalSdk | undefined> {
    KitDemoLog.debug(TAG, "create");
    if (aiframeConfig.context === undefined) {
      return undefined;
    }
    this.sdkImpl = await MultimodalSdkCreator.getInstance().create(aiframeConfig, params, sdkConfig, listener);
    return this.sdkImpl;
  }

  public initRecognizeEngine(params: InitRecognizeParams, listener: BaseRecognizeListener): void {
    KitDemoLog.debug(TAG, "initRecognizeEngine");
    this.sdkImpl?.initRecognizeEngine(params, listener);
  }

  public initTtsEngine(params: object, listener: BaseTtsListener): void {
    KitDemoLog.debug(TAG, "initRecognizeEngine");
    this.sdkImpl?.initTtsEngine(params, listener);
  }

  public textToSpeak(params: TtsParams): void {
    KitDemoLog.debug(TAG, "textToSpeak");
    this.sdkImpl?.textToSpeak(KitConstant.XIAOYI_VOICE_AGENT_ID, params);
  }

  public startRecognize(params: StartRecognizeParams): void {
    KitDemoLog.debug(TAG, "startRecognize");
    this.sdkImpl?.startRecognize(KitConstant.XIAOYI_VOICE_AGENT_ID, params);
  }

  public registerUiListener(listener: BaseUiListener): void {
    KitDemoLog.debug(TAG, "registerUiListener");
    this.sdkImpl?.registerUiListener(listener);
  }

  public registerAbilityContextProviderListener(listener:AbilityContextProviderListener): void {
    KitDemoLog.debug(TAG, "registerContextProviderListener");
    this.sdkImpl?.registerAbilityContextProviderListener(listener);
  }

  public cancelRecognize(): void {
    KitDemoLog.debug(TAG, "cancelRecognize");
    this.sdkImpl?.cancelRecognize(KitConstant.XIAOYI_VOICE_AGENT_ID);
  }

  public updateVoiceContext(context: MultimodalContext): void {
    KitDemoLog.debug(TAG, "updateVoiceContext");
    this.sdkImpl?.updateVoiceContext(KitConstant.XIAOYI_VOICE_AGENT_ID, context);
  }

  public updateVoiceEvent(context: MultimodalEvent): void {
    KitDemoLog.debug(TAG, "updateVoiceEvent");
    this.sdkImpl?.updateVoiceEvent(KitConstant.XIAOYI_VOICE_AGENT_ID, context);
  }

  public renewSession(newSession: RenewSessionParams) {
    KitDemoLog.debug(TAG, "renewSession");
    this.sdkImpl?.renewSession(newSession);
  }

  public postMessage(message: PostMessageParams, callback:VoiceKitCallback) {
    KitDemoLog.debug(TAG, "postMessage");
    this.sdkImpl?.postMessage(message, callback);
  }

  public stopBusiness(stopParams: StopBusinessParams) {
    KitDemoLog.debug(TAG, "stopBusiness");
    this.sdkImpl?.stopBusiness(KitConstant.XIAOYI_VOICE_AGENT_ID, stopParams);
  }

  public release() {
    KitDemoLog.debug(TAG, "release");
    MultimodalSdkCreator.getInstance().release();
  }
}