import common from '@ohos.app.ability.common';
import { KitDemoEmitter } from '../base/KitDemoEmitter';
import { KitDemoEmitterEvent } from '../base/KitDemoEmitterEvent';
import { KitDemoLog } from '../base/KitDemoLogger';
import { PermissionManager } from '../base/util/PermissionManager';
import { CommonCallbackComponent } from './callbackpages/CommonCallbackComponent';
import { CreateCallbackComponent } from './callbackpages/CreateCallbackComponent';
import { RecognizeCallbackComponent } from './callbackpages/RecognizeCallbackComponent';
import { TtsCallbackComponent } from './callbackpages/TtsCallbackComponent';
import { UiCallbackComponent } from './callbackpages/UiCallbackComponent';
import { DataShareStorage } from './DataShareStorage';
import { CancelRecognizeComponent } from './parampages/CancelRecognizeComponent';
import { CreateComponent } from './parampages/CreateComponent';
import { InitRecognizeEngineComponent } from './parampages/InitRecognizeEngineComponent';
import { InitTtsEngineComponent } from './parampages/InitTtsEngineComponent';
import { PostMessageComponent } from './parampages/PostMessageComponent';
import {
  RegisterAbilityContextProviderListenerComponent
} from './parampages/RegisterAbilityContextProviderListenerComponent';
import { RegisterUiListenerComponent } from './parampages/RegisterUiListenerComponent';
import { ReleaseComponent } from './parampages/ReleaseComponent';
import { RenewSessionComponent } from './parampages/RenewSessionComponent';
import { StartRecognizeComponent } from './parampages/StartRecognizeComponent';
import { StopBusinessComponent } from './parampages/StopBusinessComponent';
import { TextToSpeakComponent } from './parampages/TextToSpeakComponent';
import { UpdateVoiceContextComponent } from './parampages/UpdateVoiceContextComponent';
import { UpdateVoiceEventComponent } from './parampages/UpdateVoiceEventComponent';

const TAG: string = 'DemoEntry'

let storage: LocalStorage = new LocalStorage(
  {
    // 执行按钮是否可点击
    'executeButtonEnable': true,

    // create接口回调
    'onCreate': '',
    'createOnError': '',

    // recognize接口回调
    'initRecognizeEngineOnInit': '',
    'initRecognizeEngineOnError': '',
    'onRecordStart': '',
    'onRecordEnd': '',
    'onSpeechStart': '',
    'onSpeechEnd': '',
    'startRecord': '',
    'stopRecord': '',
    'onVolumeGet': '',
    'onPartialAsrResult': '',
    'onDmResult': '',
    'onCancel': '',

    // tts接口回调
    'onTtsInit': '',
    'onTtsError': '',
    'onTtsStart': '',
    'onTtsFormatChange': '',
    'onTtsDataProgress': '',
    'onTtsDataProgressChanged': '',
    'onTtsDataFinish': '',
    'onTtsPhonemeProgress': '',
    'onTtsPhonemeFinish': '',
    'onTtsComplete': '',
    'onTtsDownloadToneEngine': '',

    // Ui接口回调
    'onControlDisplay': '',
    'onAsrDisplay': '',
    'onDisplayStreamText': '',
    'onCardDisplay': '',
    'onVolumeDisplay': '',
    'onSysFlowDisplay': '',
    'onChipsDisplay': '',
    'onReceive': '',

    // 其他非识别请求回调
    'nonRecResultCode': '',
    'nonRecResponse': ''
  } as Record<string, string | boolean>);

DataShareStorage.getInstance().setLocalStorage(storage);

@Entry(storage)
@Component
struct Index {
  @State index: number = 0;
  @State state: boolean = false;
  @LocalStorageLink('executeButtonEnable') executeButtonEnable: boolean = true;
  scroller: Scroller = new Scroller()

  aboutToAppear(): void {
    PermissionManager.requestPermission(getContext(this) as common.UIAbilityContext,
      PermissionManager.PERMISSIONS_USER_AGENT)
      .then((result: boolean) => {
        if (!result) {
          KitDemoLog.error(TAG, 'request permission failed.');
        } else {
          KitDemoLog.info(TAG, 'request permission success.');
        }
      })
  }

  build() {
    Column() {
      Column() {
        Row() {
          Text("选择接口").textAlign(TextAlign.Start)
            .font({ size: 15, weight: 800 })
            .padding(10)
          Select([{ value: 'Create' },
            { value: 'InitRecognizeEngine' },
            { value: 'InitTtsEngine' },
            { value: 'RegisterUiListener' },
            { value: 'RegisterAbilityContextProviderListener' },
            { value: 'StartRecognize' },
            { value: 'UpdateVoiceContext' },
            { value: 'UpdateVoiceEvent' },
            { value: 'CancelRecognize' },
            { value: 'TextToSpeak' },
            { value: 'RenewSession' },
            { value: 'PostMessage' },
            { value: 'StopBusiness' },
            { value: 'Release' }
          ])
            .selected(0)
            .value('Create')
            .font({ size: 15, weight: 400 })
            .fontColor('#182431')
            .selectedOptionFont({ size: 15, weight: 400 })
            .optionFont({ size: 15, weight: 400 })
            .onSelect((index: number) => {
              this.index = index;
            })
            .layoutWeight(1)
        }

        Column() {
          Text("接口参数").textAlign(TextAlign.Start)
            .font({ size: 15, weight: 800 })
            .padding(10)
          Scroll(this.scroller) {
            Column() {
              if (this.index === 0) {
                CreateComponent()
              } else if (this.index === 1) {
                InitRecognizeEngineComponent()
              } else if (this.index === 2) {
                InitTtsEngineComponent()
              } else if (this.index === 3) {
                RegisterUiListenerComponent()
              } else if (this.index === 4) {
                RegisterAbilityContextProviderListenerComponent()
              } else if (this.index === 5) {
                StartRecognizeComponent()
              } else if (this.index === 6) {
                UpdateVoiceContextComponent()
              } else if (this.index === 7) {
                UpdateVoiceEventComponent()
              } else if (this.index === 8) {
                CancelRecognizeComponent()
              } else if (this.index === 9) {
                TextToSpeakComponent()
              } else if (this.index === 10) {
                RenewSessionComponent()
              } else if (this.index === 11) {
                PostMessageComponent()
              } else if (this.index === 12) {
                StopBusinessComponent()
              } else if (this.index === 13) {
                ReleaseComponent()
              }
            }.alignItems(HorizontalAlign.Start)
          }
          .scrollable(ScrollDirection.Vertical) // 滚动方向纵向
          .scrollBarColor(Color.Gray) // 滚动条颜色
          .scrollBarWidth(5) // 滚动条宽度
          .edgeEffect(EdgeEffect.Spring)
          .layoutWeight(1)

        }.alignItems(HorizontalAlign.Start).height(180)

        Button('执行', { type: ButtonType.Normal, stateEffect: true })
          .borderRadius(8)
          .backgroundColor(0x317aff)
          .onClick(() => {
            KitDemoEmitter.getInstance().post(KitDemoEmitterEvent.EXECUTE_BUTTON_CLICK);
          })
          .enabled(this.executeButtonEnable)
          .width('95%')
          .margin({ top: 10, right: 10, bottom: 10, left: 10 })
      }.alignItems(HorizontalAlign.Start)

      Divider().strokeWidth(2).color('#F1F3F5')
      Column() {
        Scroll(this.scroller) {
          Column() {
            CreateCallbackComponent()
            RecognizeCallbackComponent()
            TtsCallbackComponent()
            UiCallbackComponent()
            CommonCallbackComponent()
          }.alignItems(HorizontalAlign.Start)
        }
        .scrollable(ScrollDirection.Vertical) // 滚动方向纵向
        .scrollBarColor(Color.Gray) // 滚动条颜色
        .scrollBarWidth(5) // 滚动条宽度
        .edgeEffect(EdgeEffect.Spring)
      }.alignItems(HorizontalAlign.Start).height(450)
    }
  }
}