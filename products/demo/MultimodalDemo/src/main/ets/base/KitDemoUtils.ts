/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import util from '@ohos.util';
import { KitDemoLog } from './KitDemoLogger';
import { SafeJson } from '@hms-security/agoh-base-sdk';

export namespace KitDemoUtils {
  const TAG: string = 'BaseUtils';

  /**
   * Check obj is empty.
   *
   * @param {Object} obj need checked object
   * @return {boolean} true(empty)
   */
  export function isEmptyObj(obj): boolean {
    return (typeof (obj) === 'undefined' || obj === null || obj === '');
  }

  /**
   * Check str is empty.
   *
   * @param {string} str need checked string
   * @return {boolean} true(empty)
   */
  export function isEmptyStr(str: string): boolean {
    return isEmptyObj(str) || str?.trim().length === 0;
  }

  /**
   * Check array is empty.
   *
   * @param {Array} arr need checked array
   * @return {boolean} true(empty)
   */
  export function isEmptyArr(arr): boolean {
    return isEmptyObj(arr) || !Array.isArray(arr) || arr?.length === 0;
  }

  /**
   * 解析json格式字符串到javascript对象, 异常情况下返回空对象
   *
   * @param jsonData json格式的字符串
   * @returns json对象
   */
  export function parseJson(jsonData: string): any {
    try {
      return SafeJson.ohAegJsonParse(jsonData);
    } catch (error) {
      KitDemoLog.error(TAG, `${SafeJson.ohAegJsonStringify(error)}`);
      return '';
    }
  }

  /**
   * 获取唯一值uuid
   *
   * @returns uuid
   */
  export function uuid(): string {
    return util.generateRandomUUID();
  }

  /**
   * 保存并返回全局单例
   *
   * @param ObjectClass 类
   * @param storageKey 对应值
   * @returns 实例
   */
  export function createOrGet<T>(ObjectClass: { new(): T }, storageKey: string): T {
    if (KitDemoUtils.isEmptyObj(globalThis.vaSingleInstance)) {
      KitDemoLog.debug(TAG, 'init vaSingleInstance');
      globalThis.vaSingleInstance = {};
    }

    if (!globalThis.vaSingleInstance[storageKey]) {
      globalThis.vaSingleInstance[storageKey] = new ObjectClass();
      KitDemoLog.debug(TAG, `Create key of ${storageKey}`);
    }
    KitDemoLog.debug(TAG, `get key of ${storageKey}`);
    return globalThis.vaSingleInstance[storageKey];
  }
}