import {
  AbilityContextProviderListener
} from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/abilitycontext/AbilityContextProviderListener';
import {
  AbilityContextType
} from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/abilitycontext/AbilityContextProviderListener';
import common from '@ohos.app.ability.common';
import { KitSdkConnector } from '../../sdk/KitSdkConnector';
import { KitDemoLog } from '../KitDemoLogger';
import { SafeJson } from '@hms-security/agoh-base-sdk';

const TAG = 'KitDemoAbilityContextProviderListener'

export class KitDemoAbilityContextProviderListener implements AbilityContextProviderListener {
  onProvider(contextTypeList: AbilityContextType[]): common.UIAbilityContext
    | common.ServiceExtensionContext | undefined {
    KitDemoLog.info(TAG, `onProvider: ${SafeJson.ohAegJsonStringify(contextTypeList)}`)
    return KitSdkConnector.getInstance().getUiAbilityContext();
  }
}