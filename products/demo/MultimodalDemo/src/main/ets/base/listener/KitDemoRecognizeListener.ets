/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import { BaseRecognizeListener } from '@hms-assistant/common-aikit';
import { DisplayAsrPayload } from '@hms-assistant/common-aikit/src/main/ets/base/bean/ui/DisplayAsrPayload';
import { UiMessage } from '@hms-assistant/common-aikit/src/main/ets/base/const/UiMessage';
import { DataShareStorage } from '../../pages/DataShareStorage';
import { KitDemoLog } from '../KitDemoLogger';

const TAG = 'KitDemoInitRecognizeListener'

export class KitDemoRecognizeListener extends  BaseRecognizeListener {
  onInit() {
    KitDemoLog.info(TAG, 'init recognize success.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('initRecognizeEngineOnInit', 'success');
  }

  onSpeechStart() {
    KitDemoLog.info(TAG, 'onSpeechStart.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onSpeechStart', 'speech start');
  }

  onSpeechEnd() {
    KitDemoLog.info(TAG, 'onSpeechEnd.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onSpeechEnd', 'speech end');
  }

  onRecordStart() {
    KitDemoLog.info(TAG, 'onRecordStart.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onRecordStart', 'record start');
  }

  onRecordEnd() {
    KitDemoLog.info(TAG, 'onRecordEnd.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onRecordEnd', 'record end');
  }

  onCancel() {
    KitDemoLog.info(TAG, 'onCancel.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onCancel', 'cancel');
  }

  onError(agentId:string, errorCode: number, errorMsg: string) {
    KitDemoLog.info(TAG, 'onError.');
    DataShareStorage.getInstance()
      .getLocalStorage()?.setOrCreate('recognizeEngineOnError', `errorCode:${errorCode}, errorMsg:${errorMsg}`);
  }

  onPartialResult(data: UiMessage.UiMessageData) {
    let asrPayload: DisplayAsrPayload = data.uiMsg as DisplayAsrPayload;
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onPartialAsrResult', asrPayload?.text);
  }

  onBallState(state: number) {
  }

  onWriteStart(): void {
  }
}