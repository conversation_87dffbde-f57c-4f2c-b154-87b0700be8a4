import { BaseUiListener, StartRecognizeParams } from '@hms-assistant/common-aikit';
import { RecognizeDataType } from '@hms-assistant/common-aikitbase/src/main/ets/base/const/AikitConst';
import { UiMessage } from '@hms-assistant/common-aikit/src/main/ets/base/const/UiMessage';
import { DataShareStorage } from '../../pages/DataShareStorage';
import { KitSdkConnector } from '../../sdk/KitSdkConnector';
import { KitDemoLog } from '../KitDemoLogger';
import { BaseUtils } from '@hms-assistant/common-corebase/src/main/ets/util/BaseUtils';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import { STORAGE_KEY_IS_ALARM_RING,STORAGE_KEY_SUMMARY_STATE } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { DemoClientContextBuilder } from '../util/DemoClientContextBuilder';

const TAG = 'KitDemoUiListener'

export class KitDemoUiListener extends BaseUiListener {
  constructor() {
    super();
    this.initEventMap();
  }

  private initEventMap() {
    this.eventMapper.set(UiMessage.UiDisplay.SYS_FLOW_STATE, (message: UiMessage.UiMessageData) => {
      DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onSysFlowDisplay', message?.uiMsg?.content);
    });

    this.eventMapper.set(UiMessage.UiControl.CONTROL, (message: UiMessage.UiMessageData) => {
      if (message?.uiMsg?.content === 'START_RECORD') {
        KitDemoLog.log(TAG, `receive START_RECORD msg`);
        let params = new StartRecognizeParams();
        params.acquisitionParams.selfRecord = false;
        params.acquisitionParams.isNeedOpus = true;
        params.acquisitionParams.dataType = RecognizeDataType.AUDIO;
        KitSdkConnector.getInstance().startRecognize(params);
      }
    });
    this.eventMapper.set(UiMessage.UiControl.CONTROL_APP_RECOGNIZE, this.startAppRecognize);
    this.eventMapper.set(UiMessage.DataShare.DATA_SHARE, this.handleDataShare);
  }

  private handleDataShare(message: UiMessage.UiMessageData): void {
    let data = message?.uiMsg?.content as UiMessage.SharedData;
    Logger.debug(TAG, `data: ${SafeJson.ohAegJsonStringify(data)}}`);
    if (data.dataKey === STORAGE_KEY_IS_ALARM_RING) {
      AppStorage.setOrCreate<boolean>(STORAGE_KEY_IS_ALARM_RING, data.dataValue === 'true');
    } else if (data.dataKey === STORAGE_KEY_SUMMARY_STATE) {
      AppStorage.setOrCreate<string>(STORAGE_KEY_SUMMARY_STATE, data.dataValue);
    } else {
      Logger.debug(TAG, `handleDataShare: ${SafeJson.ohAegJsonStringify(message)}}`);
    }
  }

  private startAppRecognize(message: UiMessage.UiMessageData): void {
    let startRecognizeParams: StartRecognizeParams = message?.uiMsg?.content as StartRecognizeParams;
    if (BaseUtils.isEmptyObj(startRecognizeParams)) {
      Logger.error(TAG, `param is empty.`);
      return;
    }
    let currentDataType = startRecognizeParams.acquisitionParams.dataType;
    if (currentDataType === RecognizeDataType.AUDIO) {
      KitSdkConnector.getInstance().startRecognize(startRecognizeParams);
    } else if (currentDataType === RecognizeDataType.TEXT) {
      this.prepareCommonContext().then(contextBuilder => {
        startRecognizeParams.recognizeContext.contexts = [contextBuilder.build()];
      });
      KitSdkConnector.getInstance().startRecognize(startRecognizeParams);
    } else {
      Logger.debug(TAG, `startAppRecognize with other type:${currentDataType}`);
    }
  }

  private prepareCommonContext(businessType?: string, isSupportRejection?: boolean): Promise<DemoClientContextBuilder> {
    return new Promise((resolve, reject) => {
      let builder = new DemoClientContextBuilder()
        .setSupportRejection(isSupportRejection ?? true)
        .setStartMode()
        .setBusinessType(businessType)
        .setRefQaMode()
        .setForegroundApp()
        .setIsAlarmRing()
        .setServiceCenterData();
      resolve(builder);
    });
  }
}