/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { MultimodalSdk, SessionChangeListener } from '@hms-assistant/common-aikit';
import { AiKitError, AiKitSession } from '@hms-assistant/common-aikitbase';
import { DataShareStorage } from '../../pages/DataShareStorage';
import { KitSdkConnector } from '../../sdk/KitSdkConnector';
import { KitDemoLog } from '../KitDemoLogger';

const TAG = 'KitSessionChangeListener'

export class KitSessionChangeListener implements SessionChangeListener {
  onCreate(sdk: MultimodalSdk) {
    KitDemoLog.info(TAG, 'create sdk success.');
    KitSdkConnector.getInstance().setSdkImpl(sdk);
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onCreate', 'success');
  }

  onError(errorCode: AiKitError.ErrorInfo) {
    KitDemoLog.info(TAG, 'create sdk failed.');
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('createOnError', errorCode.errorMsg);
  }

  onSessionChange(currentSession: AiKitSession): void {
    KitDemoLog.info(TAG, 'onSessionChange.');
  }
}