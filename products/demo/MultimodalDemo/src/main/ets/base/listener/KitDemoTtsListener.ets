/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { BaseTtsListener } from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/tts/BaseTtsListener';
import { DataShareStorage } from '../../pages/DataShareStorage';
import { KitDemoLog } from '../KitDemoLogger';

const TAG = 'KitDemoInitRecognizeListener'

export class KitDemoTtsListener extends  BaseTtsListener {
  /**
   * 初始化成功回调
   */
  onInit(): void {
    KitDemoLog.debug(TAG, "onInit success");
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onTtsInit', 'success');
  }

  /**
   * 初始化失败回调
   */
  onInitFail(): void {
    KitDemoLog.debug(TAG, "onInit fail");
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onTtsError', 'init failed');
  }

  /**
   * TTS播报完成，包含tts正常结束和tts被打断导致的结束
   *
   * @param utteranceId 当前播报的id，与调用textToSpeak时传入的 RecognizerIntent. EXT_UTTERANCE_ID相同，不传则随机生成
   */
  onTtsComplete(utteranceId: object): void {
    KitDemoLog.debug(TAG, `onTtsComplete: ${utteranceId}`);
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onTtsComplete', 'tts complete');
  }

  /**
   * 开始TTS播报
   *
   * @param intent 回调消息
   */
  onTtsStart(params: object): void {
    KitDemoLog.debug(TAG, "onTtsStart");
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onTtsStart', 'tts start');
  }

  /**
   * TTS播报错误
   *
   * @param errorCode 错误码
   * @param errorMsg 错误信息
   * @param utteranceId 当前播报的id，与调用textToSpeak时传入的 RecognizerIntent. EXT_UTTERANCE_ID相同，不传则随机生成
   */
  onTtsError(errorCode: number, errorMsg: string, utteranceId: string): void {
    KitDemoLog.error(TAG, "onTtsError");
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onTtsError', `errorCode:${errorCode}, errorMsg:${errorMsg}`);
  }

  /**
   * TTS播报进度回调
   *
   * @param utteranceId 播报标识
   * @param progress 播报进度值
   */
  onTtsProgressChanged(utteranceId: string, progress: number): void {
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('onTtsDataProgressChanged', progress);
  }

  /**
   * TTS合成进度回调
   *
   * @param utteranceId 播报标识
   * @param audioData 音频流
   * @param mimeType 流媒体类型
   */
  onTtsDataProgress(utteranceId: string, audioData: ArrayBuffer, mimeType: number): void {
    KitDemoLog.debug(TAG, "onTtsDataProgress");
  }

  /**
   * TTS合成结束
   *
   * @param utteranceId 播报标识
   */
  onTtsDataFinish(utteranceId: string): void {
    KitDemoLog.debug(TAG, "onTtsDataFinish");
  }

  /**
   * TTS音素合成进度回调
   *
   * @param utteranceId 标识
   * @param data 音素流数据
   * @param type 音素流类型，该字段为预留字段，默认值为0，标识默认音素类型
   * @param extend 预留字段，用于未来扩展使用
   */
  onPhonemeProgress(utteranceId: string, data: string, type: number): void {
    KitDemoLog.debug(TAG, "onPhonemeProgress");
  }

  /**
   * TTS音素流合成完成回调
   *
   * @param utteranceId 标识
   * @param extend 预留字段，用于未来扩展使用
   */
  onPhonemeFinish(utteranceId: string): void {
    KitDemoLog.debug(TAG, "onPhonemeFinish");
  }
}