/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import {
  VoiceKitCallback,
  VoiceKitCallbackInfo
} from '@hms-assistant/common-aikit/src/main/ets/logicmodule/north/interface/cloudaccess/CloudAccessInterface';
import { DataShareStorage } from '../../pages/DataShareStorage';
import { KitDemoLog } from '../KitDemoLogger';
import { SafeJson } from '@hms-security/agoh-base-sdk';

const TAG = 'VoiceKitDemoCallbackImpl'

export class VoiceKitDemoCallbackImpl implements VoiceKitCallback {
  onCallback(callbackInfo: VoiceKitCallbackInfo) {
    KitDemoLog.info(TAG, `callback info: ${SafeJson.ohAegJsonStringify(callbackInfo)}`);
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('nonRecResultCode', callbackInfo.resultCode);
    DataShareStorage.getInstance().getLocalStorage()?.setOrCreate('nonRecResponse', callbackInfo.content);
  }
}