/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import Emitter from '@ohos.events.emitter';
import type { KitDemoEmitterEvent } from './KitDemoEmitterEvent';
import { KitDemoLog } from './KitDemoLogger';

const TAG = 'KitDemoEmitterUnit';

export class KitDemoEmitterUnit {
  name: string = '';
  callback?: (content: any) => void;
}

export class KitDemoEmitter {
  private readonly innerEvent: number = 1;
  private eventUnits: Map<KitDemoEmitterEvent, KitDemoEmitterUnit[]> = new Map<KitDemoEmitterEvent, KitDemoEmitterUnit[]>();
  private initialized: boolean = false;

  private static instance: KitDemoEmitter = new KitDemoEmitter();

  private constructor() {
  }

  public static getInstance(): KitDemoEmitter {
    return KitDemoEmitter.instance;
  }

  public init(): void {
    if (this.initialized === true) {
      return;
    }
    Emitter.on({ eventId: this.innerEvent }, (eventData: Emitter.EventData) => {
      let units: KitDemoEmitterUnit[] | undefined = this.eventUnits.get(eventData?.data?.event);

      let content = eventData?.data?.content;
        units?.forEach((item: KitDemoEmitterUnit) => {
          item.callback?.(content);
        });
    });
    this.initialized = true;
  }

  public release(): void {
    Emitter.off(this.innerEvent);
    this.eventUnits.clear();
    this.initialized = false;
  }

  public register(event: KitDemoEmitterEvent, unit: KitDemoEmitterUnit): void {
    if (this.eventUnits.has(event)) {
      let units: KitDemoEmitterUnit[] = this.eventUnits.get(event);
      let index: number = units.findIndex((item: KitDemoEmitterUnit) => item.name === unit.name);
      if (index === -1) {
        units.push(unit);
      } else {
        units[index].callback = unit.callback;
      }
    } else {
      this.eventUnits.set(event, [unit]);
    }
    KitDemoLog.info(TAG, `register event ${event}, lenght is ${this.eventUnits.get(event).length}`);
  }

  public unregister(event: KitDemoEmitterEvent, unit: KitDemoEmitterUnit): void {
    if (this.eventUnits.has(event)) {
      let units: KitDemoEmitterUnit[] = this.eventUnits.get(event);
      let index: number = units.findIndex((item: KitDemoEmitterUnit) => item.name === unit.name);
      if (index !== -1) {
        if (units.length > 1) {
          units.splice(index, 1);
        } else {
          this.eventUnits.delete(event);
        }
      }
    }
  }

  public post(event: KitDemoEmitterEvent, content?: any): void {
    Emitter.emit({ eventId: this.innerEvent }, { data: { 'event': event, 'content': content } });
  }

  public postDelayed(delayMillis: number, event: KitDemoEmitterEvent, content?: any): void {
    setTimeout(() => {
      this.post(event, content);
    }, delayMillis);
  }
}

