/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import AbilityAccessCtrl from '@ohos.abilityAccessCtrl';
import type { Permissions } from '@ohos.abilityAccessCtrl';
import BundleManager from '@ohos.bundle.bundleManager';
import type Common from '@ohos.app.ability.common';
import { BaseUtils } from '@hms-assistant/common-aikit/src/main/ets/base/util/BaseUtils';
import { KitDemoLog } from '../KitDemoLogger';
import { SafeJson } from '@hms-security/agoh-base-sdk';

export namespace PermissionManager {
  const TAG: string = 'PermissionManager';

  // 授权方式为user_grant的权限需要通过用户手动授权获取
  // https://gitee.com/openharmony/docs/blob/master/zh-cn/application-dev/security/permission-list.md
  export const PERMISSIONS_USER_AGENT: Array<Permissions> = [
    "ohos.permission.MICROPHONE",
    "ohos.permission.LOCATION",
    "ohos.permission.APPROXIMATELY_LOCATION"
  ];

  /**
   * 申请权限
   *
   * @param context 必须是AbilityContext
   * @param permissions 需要申请的权限
   * @return boolean true 成功, false 失败
   */
  export async function requestPermission(context: Common.UIAbilityContext,
                                          permissions: Array<Permissions>): Promise<boolean> {
    KitDemoLog.info(TAG, 'requestPermission in');
    let bundleFlag = 0;
    let appInfo =
      await BundleManager.getApplicationInfo(context?.applicationInfo.name ?? 'com.huawei.hmos.vassistant', bundleFlag);
    let tokenID = appInfo.accessTokenId;
    KitDemoLog.info(TAG, 'tokenID = ' + tokenID);
    let atManager = AbilityAccessCtrl.createAtManager();
    let permissionsDenied: Array<Permissions> = [];

    for (let i = 0;i < permissions.length; i++) {
      let result = await atManager.checkAccessToken(tokenID, permissions[i]);
      if (result === AbilityAccessCtrl.GrantStatus.PERMISSION_DENIED) {
        permissionsDenied.push(permissions[i]);
      }
    }

    let result: boolean = true;
    if (permissionsDenied.length <= 0) {
      KitDemoLog.debug(TAG, 'need not request permission');
      return result;
    }
    KitDemoLog.debug(TAG, 'permissions :', permissionsDenied);

    return new Promise((resolve, reject) => {
      atManager.requestPermissionsFromUser(context, permissions).then((data) => {
        KitDemoLog.info(TAG, 'requestPerm result = ' + SafeJson.ohAegJsonStringify(data));
        result = isPermissionGranted(data);
        resolve(result);
      }, (err) => {
        KitDemoLog.error(TAG, 'requestPerm error:' + err.message);
        reject(err.message);
      });
    });
  }

  /**
   * 检查用户是否已经授予权限
   *
   * @param result 系统返回的权限授权信息
   */
  function isPermissionGranted(result): boolean {
    KitDemoLog.debug(TAG, 'isPermissionGranted in');
    let isGranted = true;
    if (BaseUtils.isEmptyObj(result)) {
      KitDemoLog.debug(TAG, 'result is empty');
      return isGranted;
    }
    if (BaseUtils.isEmptyArr(result.authResults) || BaseUtils.isEmptyArr(result.permissions)) {
      KitDemoLog.debug(TAG, 'result  array is empty');
      return isGranted;
    }

    for (let i = 0; i < result.authResults.length; i++) {
      if (result.authResults[i] === AbilityAccessCtrl.GrantStatus.PERMISSION_DENIED) {
        KitDemoLog.info(TAG, 'Permission ' + result.permissions[i] + ' is denied');
        return false;
      }
    }
    return isGranted;
  }

  /**
   * 检查是否已经有指定的权限
   *
   * @param permissions 需要检查的权限
   * @return boolean true 有, false 无
   */
  export async function checkPermission(permissions: Array<Permissions>): Promise<boolean> {
    KitDemoLog.debug(TAG, 'checkPermission in');
    let bundleFlag = 0;
    let appInfo = await BundleManager.getApplicationInfo('com.huawei.hmos.vassistant', bundleFlag);
    let tokenID = appInfo.accessTokenId;
    let atManager = AbilityAccessCtrl.createAtManager();

    for (let i = 0;i < permissions.length; i++) {
      let result = await atManager.checkAccessToken(tokenID, permissions[i]);
      if (result === AbilityAccessCtrl.GrantStatus.PERMISSION_DENIED) {
        return false;
      }
    }
    KitDemoLog.debug(TAG, 'checkPermission out');
    return true;
  }
}