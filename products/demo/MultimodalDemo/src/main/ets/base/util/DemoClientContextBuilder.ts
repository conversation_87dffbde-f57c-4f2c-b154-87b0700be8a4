/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import {
  STORAGE_KEY_CLOUD_DRAG_TEXT,
  STORAGE_KEY_DRAG_TEXT_FROM,
  STORAGE_KEY_IS_ALARM_RING,
  STORAGE_KEY_IS_REFQA_MODE,
  STORAGE_KEY_LAUNCH_TYPE,
  STORAGE_FORWARD_BUNDLE_NAME
} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { AppConstants } from '@hms-assistant/common-corebase/src/main/ets/constant/AppConstants';
import { BaseUtils } from '@hms-assistant/common-aikit/src/main/ets/base/util/BaseUtils';
import { FloatScreenMgt } from '@hms-assistant/common-corebase/src/main/ets/util/FloatScreenMgt';
import { HeaderPayload } from '@hms-assistant/common-aikitbase/src/main/ets/base/const/AikitConst';
import { SettingDbHelper } from '@hms-assistant/common-storage/src/main/ets/database/SettingDbHelper';
import { systemSettingKey } from '@hms-assistant/common-storage/src/main/ets/keys/SystemSettingKey';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';

const TAG: string = 'ClientContextBuilder';

export class DemoClientContextBuilder {
  clientContext: HeaderPayload = new HeaderPayload('System', 'ClientContext');
  payload: { [key: string]: any } = {};

  public build(): HeaderPayload {
    this.clientContext.payload = this.payload;
    return this.clientContext;
  }

  public setSupportRejection(isSupportRejection: boolean): DemoClientContextBuilder {
    this.payload.isSupportRejection = isSupportRejection;
    return this;
  }

  public setStartMode(): DemoClientContextBuilder {
    let launchType: string = AppStorage.get(STORAGE_KEY_LAUNCH_TYPE);
    let startMode: string = AppConstants.START_MODE[launchType];
    if (!BaseUtils.isEmptyStr(startMode)) {
      this.payload.startMode = startMode;
    }
    return this;
  }

  public setBusinessType(businessType?: string): DemoClientContextBuilder {
    if (BaseUtils.isEmptyStr(businessType)) {
      if (FloatScreenMgt.getInstance().isHalfActive() || FloatScreenMgt.getInstance().isOnAppActive()) {
        this.payload.businessType = 'HalfScreen';
      } else if (FloatScreenMgt.getInstance().isFullActive()) {
        this.payload.businessType = 'NormalScreen';
      }
    } else {
      this.payload.businessType = businessType;
    }
    return this;
  }

  public setDragText(): DemoClientContextBuilder {
    let dragText: string = AppStorage.get(STORAGE_KEY_CLOUD_DRAG_TEXT);
    let dragTextFrom: string | undefined = AppStorage.get(STORAGE_KEY_DRAG_TEXT_FROM);
    if (dragTextFrom === undefined || dragTextFrom !== 'WeakNotify') {
      AppStorage.delete(STORAGE_KEY_CLOUD_DRAG_TEXT);
    }
    if (!BaseUtils.isEmptyStr(dragText)) {
      this.payload.dragText = dragText;
    }
    return this;
  }

  public setIsAlarmRing(): DemoClientContextBuilder {
    // 上传闹钟响铃状态
    if (AppStorage.has(STORAGE_KEY_IS_ALARM_RING)) {
      this.payload.isAlarmRing = AppStorage.get(STORAGE_KEY_IS_ALARM_RING) ?? false;
      AppStorage.delete(STORAGE_KEY_IS_ALARM_RING);
    }
    return this;
  }

  public setRefQaMode(): DemoClientContextBuilder {
    // 是否进入文档摘要模式
    if (AppStorage.has(STORAGE_KEY_IS_REFQA_MODE)) {
      let isRefQaMode: boolean = AppStorage.get(STORAGE_KEY_IS_REFQA_MODE);
      this.payload.isRefQaMode = isRefQaMode;
    }
    return this;
  }

  public setClarifyType(clarifyType?: string): DemoClientContextBuilder {
    if (clarifyType) {
      this.payload.clarifyType = clarifyType;
    }
    return this;
  }

  public setForegroundApp(): DemoClientContextBuilder {
    if (AppStorage.has(STORAGE_FORWARD_BUNDLE_NAME)) {
      this.payload.foregroundApp = {};
      this.payload.foregroundApp.packageName = AppStorage.get(STORAGE_FORWARD_BUNDLE_NAME);
    }
    return this;
  }

  public setServiceCenterData(): DemoClientContextBuilder {
    this.payload.serviceCenterData = [{
      "featureType": "CONTENT_CARD",
      "featureVersion": "15.0"
    }];
    return this;
  }

  public setSupportUnderLockScreen(): Promise<DemoClientContextBuilder> {
    // 是否在锁屏下可使用智慧语音
    return new Promise((resolve, reject) => {
      let settings: Array<Record<string, string>> = [];
      let supportUnderLockScreenPromise = SettingDbHelper.getInstance().get(systemSettingKey.LOCK_VOICE_SKILL_SWITCH);
      let supportNavigationPromise =
        SettingDbHelper.getInstance().get(systemSettingKey.LOCK_VOICE_SKILL_NAVIGATION_SWITCH);
      let supportSmartHomePromise =
        SettingDbHelper.getInstance().get(systemSettingKey.LOCK_VOICE_SKILL_SMART_HOME_SWITCH);
      let supportClockPromise =
        SettingDbHelper.getInstance().get(systemSettingKey.LOCK_VOICE_SKILL_ALARM_SCHEDULE_SWITCH);
      let supportCallPromise = SettingDbHelper.getInstance().get(systemSettingKey.LOCK_VOICE_SKILL_TELEPHONE_SWITCH);
      let settingsArr: Array<Promise<string>> = [supportUnderLockScreenPromise, supportNavigationPromise,
        supportSmartHomePromise, supportClockPromise, supportCallPromise];
      Promise.all(settingsArr)
        .then((arr: string[]) => {
          settings.push({ 'supportUnderLockScreen': arr[0] ?? AppConstants.SKILL_OFF });
          settings.push({ 'supportNavigationUnderLockScreen': arr[1] ?? AppConstants.SKILL_OFF });
          settings.push({ 'supportSmartHomeUnderLockScreen': arr[2] ?? AppConstants.SKILL_OFF });
          settings.push({ 'supportClockUnderLockScreen': arr[3] ?? AppConstants.SKILL_OFF });
          settings.push({ 'supportCallUnderLockScreen': arr[4] ?? AppConstants.SKILL_OFF });
          this.payload.settings = settings;
          resolve(this);
        }).catch((err) => {
        Logger.error(TAG, "error code is " + err?.code + " errorMessage is " + err?.message);
        reject();
      });
    });
  }
}