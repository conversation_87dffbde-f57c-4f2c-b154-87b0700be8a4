/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import geoLocationManager from '@ohos.geoLocationManager';
import type { Permissions } from '@ohos.abilityAccessCtrl';
import { PermissionManager } from './PermissionManager';
import { KitDemoLog } from '../KitDemoLogger';
import { SafeJson } from '@hms-security/agoh-base-sdk';

export namespace LocationManger {
    const TAG: string = 'LocationManger';
    const LOCATION_SYSTEM_DEFAULT: string = 'WGS84';
    const LOCATION_TIMEOUT: number = 5000;

    const LOCATION_PERMISSION: Array<Permissions> = [
        'ohos.permission.APPROXIMATELY_LOCATION'
    ];

    export enum LocationErrorCode {
        SUCCESS = 0,
        GPS_OFF = 1,
        LOCATION_PERMISSION_DENIED = 2,
        CITY_NOT_SET = 3,
        OTHER = 4
    }

    export interface LocationInfoBean {
        latitude: number;
        longitude: number;
        locationSystem: string;
        errorCode: number;
        address?: string;
        province?: string;
        city?: string;
        district?: string;
        area?: string;
    }

    export async function getLocation(): Promise<LocationInfoBean> {
        KitDemoLog.info(TAG, 'getLocation in');

        let locationInfo: LocationInfoBean = {
            latitude: 0,
            longitude: 0,
            locationSystem: LOCATION_SYSTEM_DEFAULT,
            errorCode: LocationErrorCode.SUCCESS
        }

        return PermissionManager.checkPermission(LOCATION_PERMISSION).then(hasPermission => { // 检查权限
            if (hasPermission) {
                return Promise.resolve(locationInfo);
            } else {
                KitDemoLog.error(TAG, 'no permission');
                locationInfo.errorCode = LocationErrorCode.LOCATION_PERMISSION_DENIED;
                return Promise.reject(locationInfo);
            }
        }).then((data: LocationInfoBean) => { // 检查位置开关
            // 检查位置开关
            let isLocationOn: boolean = geoLocationManager.isLocationEnabled()
            if (isLocationOn) {
                return Promise.resolve(data);
            } else {
                KitDemoLog.error(TAG, 'gps is off');
                data.errorCode = LocationErrorCode.GPS_OFF;
                return Promise.reject(data);
            }
        }).then((data: LocationInfoBean) => { // 获取经纬度
            return getCurrentLocation(data);
        }).catch((data: LocationInfoBean) => {
            KitDemoLog.error(TAG, `data = ${SafeJson.ohAegJsonStringify(data)}`);
            return Promise.resolve(data);
        });
    }

    /**
     * 获取当前经纬度
     * @param data 位置信息
     * @returns PromiseData
     */
    function getCurrentLocation(data: LocationInfoBean): Promise<LocationInfoBean> {
        KitDemoLog.info(TAG, 'getCurrentLocation in');
        // 获取经纬度超时
        let timeoutId: number = 0;
        let promiseTimeout = new Promise((resolve, reject) => {
            timeoutId = setTimeout(() => {
                KitDemoLog.info(TAG, 'location timeout');
                reject(data);
            }, LOCATION_TIMEOUT);
        })

        let locationRequest: geoLocationManager.CurrentLocationRequest = {
            priority: geoLocationManager.LocationRequestPriority.FIRST_FIX, //快速拿到位置
            scenario: geoLocationManager.LocationRequestScenario.DAILY_LIFE_SERVICE,
            maxAccuracy: 1000, // 表示精度信息，单位是米
            timeoutMs: 20000 //超时时间，单位是毫秒
        };

        let promiseGetLocation = geoLocationManager.getCurrentLocation(locationRequest);

        return Promise.race([promiseTimeout, promiseGetLocation]).then((location: geoLocationManager.Location) => {
            KitDemoLog.info(TAG, `location = ${SafeJson.ohAegJsonStringify(location)}`);
            clearTimeout(timeoutId);
            data.longitude = location.longitude;
            data.latitude = location.latitude;
            return Promise.resolve(data);
        }).catch((err) => {
            KitDemoLog.error(TAG, `error : ${SafeJson.ohAegJsonStringify(err)}`);
            // 获取经纬度失败, 包括定时器超时、getCurrentLocation失败两种场景
            data.errorCode = LocationErrorCode.OTHER;
            return Promise.reject(data);
        });
    }

    /**
     * 根据经纬度获取地址信息
     */
    function getAddressByLocation(sysLocation: geoLocationManager.Location, locationInfo: LocationInfoBean): Promise<LocationInfoBean> {
        KitDemoLog.info(TAG, 'getAddressByLocation in');
        // 根据经纬度获取地址信息
        let addressRequest: geoLocationManager.ReverseGeoCodeRequest = {
            latitude: sysLocation.latitude,
            longitude: sysLocation.longitude,
            // 取一个地址信息
            maxItems: 3
        }
        return geoLocationManager.getAddressesFromLocation(addressRequest).then((addresses) => {
            if (addresses.length <= 0) {
                locationInfo.errorCode = LocationErrorCode.CITY_NOT_SET;
                KitDemoLog.error(TAG, 'no city info');
                return Promise.reject(locationInfo);
            }
            let address: geoLocationManager.GeoAddress = addresses[0];
            KitDemoLog.info(TAG, `address = ${SafeJson.ohAegJsonStringify(address)}`);
            locationInfo.longitude = address.longitude;
            locationInfo.latitude = address.latitude;
            return Promise.resolve(locationInfo);
        }).catch((err) => {
            KitDemoLog.error(TAG, `error : ${SafeJson.ohAegJsonStringify(err)}`);
            locationInfo.errorCode = LocationErrorCode.OTHER;
            return Promise.reject(locationInfo);
        });
    }
}
