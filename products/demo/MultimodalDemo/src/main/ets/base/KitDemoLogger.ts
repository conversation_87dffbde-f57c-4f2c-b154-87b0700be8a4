/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */

import { Logger as Log, HiLogNode, LogLevel } from '@hw-hmf/logger';

Log.config({
  level: LogLevel.DEBUG
})

export namespace KitDemoLog {
  const domain = 0x0013;

  const LOG_SEPARATE_LEN = 768;

  const IS_DEBUG_LOG_ENABLE: boolean = false;

  const LOG = new Log({
    node: new HiLogNode(domain),
  })

  export function log(tag: string, ...args: any[]): void {
    LOG.i(tag, args.join(' '));
  }

  export function info(tag: string, ...args: any[]): void {
    LOG.i(tag, args.join(' '));
  }

  export function debug(tag: string, ...args: any[]): void {
    if (!IS_DEBUG_LOG_ENABLE) {
      return;
    }
    LOG.d(tag, args.join(' '));
  }

  export function warn(tag: string, ...args: any[]): void {
    LOG.w(tag, args.join(' '));
  }

  export function error(tag: string, ...args: any[]): void {
    LOG.e(tag, args.join(' '));
  }

  /**
   * 开源鸿蒙单行日志长度1024字符，超长日志分成多行打印，便于获取完整的日志信息
   *
   * @param tag 日志tag
   * @param args 日志信息
   */
  export function debugLong(tag: string, ...args: any[]): void {
    if (!IS_DEBUG_LOG_ENABLE) {
      return;
    }
    let logInfo: string = args.join(' ');
    for (let i = 0; i < logInfo.length; i += LOG_SEPARATE_LEN) {
      let subStr = logInfo.substr(i, LOG_SEPARATE_LEN);
      LOG.d(tag, subStr);
    }
  }
}