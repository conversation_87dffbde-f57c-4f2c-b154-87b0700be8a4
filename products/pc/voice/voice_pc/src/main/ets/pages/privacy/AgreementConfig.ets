/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import {
  AgreementCallBack,
  AppUserAccount,
  CallBack,
  DeviceIdType,
  DeviceType,
  PafClientInfo,
  PafUserAccount,
  QueryRecordsCallBack
} from '@hms-paf/ui-widget-consent';
import AAID from '@hms.core.AAID';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { PackageUtils } from '@hms-assistant/common-corebase/src/main/ets/util/PackageUtils';
import { DeviceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/DeviceUtils';
import { AppConstants } from '@hms-assistant/common-corebase/src/main/ets/constant/AppConstants';
import deviceInfo from '@ohos.deviceInfo';
import { huaweiAccountManager } from '@hms-assistant/voice-phonebase/src/main/ets/accountManager/HuaweiAccountManager';
import { ProtocolIDConst } from '@hms-assistant/common-privacy/src/main/ets/config/StatementConfig';
import { kitModel } from '@hms-assistant/voice-voicebusiness';
import fileIo from '@ohos.file.fs';
import { ConsentGrsSysClient } from '@hms-security/consent_grs_system/src/main/ets/index';
import consentGrsSys from '@hms-security/consent_grs_system';
import consent from '@hms-security/consent';
import { PafAgreement } from '@hms-paf/ui-widget-consent/src/main/ets/consent/PafAgreement';

interface contextInfo {
  context: common.UIAbilityContext | null;
}

const TAG = 'PcAgreementConfig'

export namespace AgreementConfig {
  export const contextInfo: contextInfo = { context: null };

  //签署协议
  export class SignCallBack implements CallBack {
    onSuccess() {
      Logger.info(TAG, `PafAgreement sign onSuccess`)

    }
    onFailure(failureCode: number, detail: string) {
      Logger.info(TAG, `PafAgreement sign onFailure: code:${failureCode}, detail:${detail}`)
    }
  }

  //撤销协议
  export class RevokeCallBack implements CallBack {
    onSuccess() {
      Logger.info(TAG, `PafAgreement revoke onSuccess`)
      // 需撤销之后删除fileDir文件，里面有agis配置文件
      fileIo.rmdirSync(contextInfo?.context?.filesDir);
    }
    onFailure(failureCode: number, detail: string) {
      Logger.info(TAG, `PafAgreement revoke onFailure: code:${failureCode}, detail:${detail}`)
      fileIo.rmdirSync(contextInfo?.context?.filesDir);
    }
  }

  //查询协议记录
  export class QueryCallBack implements QueryRecordsCallBack {
    onFailure(failureCode: number, detail: string) {
      Logger.info(TAG, `PafAgreement query onFailure: code:${failureCode}, detail:${detail}`)
    }

    popupChildGrowUp() {
      Logger.info(TAG, `PafAgreement popupChildGrowUp`)
    }

    popupPermission() {
      Logger.info(TAG, `PafAgreement popupPermission`)
    }

    popupUpdate() {
      Logger.info(TAG, `PafAgreement popupUpdate`)
    }

    popupNoUpdate() {
      Logger.info(`PafAgreement popupNoUpdate`);
    }

    popupUpdateNoResign() {
      Logger.info(`PafAgreement popupUpdateNoResign`);
    }
  }

  //客户端信息（APP版本信息）
  export const getClientInfo: () => Promise<PafClientInfo> = async () => {
    let pcDeviceType: DeviceType = DeviceType.PHONE;
    switch (DeviceUtils.getDeviceInfo('devicetype')) {
      case DeviceUtils.TYPE_PHONE:
        pcDeviceType = DeviceType.PHONE
        break;
      case DeviceUtils.TYPE_TABLET:
        pcDeviceType = DeviceType.PAD;
        break;
      case DeviceUtils.TYPE_PC:
        pcDeviceType = DeviceType._2in1_;
        break;
      default:
        pcDeviceType = DeviceType.PHONE;
        break;
    }
    return {
      name: 'hmosvassistant',     //客户端名称
      version: PackageUtils.getAppVersion(AppConstants.BUNDLE_NAME),     //客户端版本
      aaid: await AAID.getAAID(),   //客户端aaid，用于标识当前客户端，客户端卸载后该值会变化，UUID格式
      deviceId: () => DeviceUtils.getDeviceInfo('odid'),   //当前设备Id
      deviceIdType: DeviceIdType.ODID,  //设备Id类型: UDID = 1, ODID = 8
      deviceType: pcDeviceType	 //设备类型: PHONE = 0,WATCH = 1,TV = 2,PC = 3,PAD = 4,SPEAKER = 5,_2in1_ = 6
    }
  }

  function getCountry(): Promise<string> {
    return new Promise<string>((pcResolve) => {
      pcResolve('cn')
    })
  }

  export function getGrsClient(context: common.Context): consent.IGrsDomainService {
    let pcGrsClient: ConsentGrsSysClient;
    Logger.error('get Grs Sys Client')
    pcGrsClient = new consentGrsSys.ConsentGrsSysClient(context);
    return pcGrsClient;
  }

  async function getAccessTokenAsync(): Promise<string> {
    return new Promise<string>((resolve) => {
      kitModel.getHwAt({
        onCallback: (info) => {
          if (info?.content) {
            resolve(info.content)
          } else {
            resolve('')
          }
        }
      })
    })
  }

  function getUserAccount(): Promise<AppUserAccount> {
    return new Promise(async (resolve, reject) => {
      let user = new AppUserAccount()
      try {
        user.country = await getCountry();
        user.userId = await huaweiAccountManager.getUid();
        user.accessToken = await getAccessTokenAsync();
        Logger.info(`PafAgreement AgreementConfig getUserAccount`);
        resolve(user);
      } catch (err) {
        reject();
      }
    })
  }

  // 签署协议需要的账户信息
  export const userAccount: () => PafUserAccount = () => {
    return {
      // batchUserAccount 一个接口一次性获取账号相关信息
      userAccount: {
        getCountry: getCountry,
        getUserId: huaweiAccountManager.getUid,
        getAccessToken: () => {
        },
        getAccessTokenAsync: getAccessTokenAsync,
        getUserIdAndCountry: getUserAccount
      }
    }
  }

  // 设置欢迎页隐私声明
  export function getAgreementTypes(): Set<() => number> {
    const agreementTypes: Set<() => number> = new Set();
    agreementTypes.add(() => {
      return ProtocolIDConst.VOICE_ASSISTANT_SERVER_PRIVACY_STATEMENT_PC
    })
    return agreementTypes
  }

  export async function init(context: Context): Promise<void> {
    AgreementConfig.contextInfo.context = (context as common.UIAbilityContext);
    let agreementCallBack: AgreementCallBack = {
      signCallBack: new AgreementConfig.SignCallBack(),
      revokeCallBack: new AgreementConfig.RevokeCallBack(),
      queryRecordsCallBack: new AgreementConfig.QueryCallBack()
    }
    let clientInfoIn: PafClientInfo = await AgreementConfig.getClientInfo()
    const contextIn: common.Context = context.createModuleContext('entry');
    let extendOption: consent.ExtendOption = new consent.ExtendOption();
    extendOption.grsClient = AgreementConfig.getGrsClient(contextIn);
    // clientInfoIn仅在初始化使用一次
    PafAgreement.initAsync(contextIn, clientInfoIn, AgreementConfig.userAccount(),
      AgreementConfig.getAgreementTypes(), agreementCallBack, 'CN', extendOption);
  }
}
