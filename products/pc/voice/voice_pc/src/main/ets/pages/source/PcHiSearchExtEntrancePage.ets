/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import {
  STORAGE_KEY_HI_SEARCH_LOAD_PAGE,
  STORAGE_KEY_HI_SEARCH_PATH } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { PageParam } from '@vassistant/ui';
import { PcHiSearchRouterMap } from '../PcHiSearchRouterMap';

let localStorage = LocalStorage.getShared();

const TAG: string = 'PcHiSearchExtEntrancePage';

/**
 * 全搜界面主入口（欢迎页）
 */
@Entry(localStorage)
@Component
struct PcHiSearchExtEntrancePage {
  @StorageLink(STORAGE_KEY_HI_SEARCH_LOAD_PAGE) navBarPageUrl: string = '';
  @StorageLink(STORAGE_KEY_HI_SEARCH_PATH) hiSearchPathInfo: NavPathStack = new NavPathStack()

  aboutToAppear() {
    Logger.debug(TAG, `aboutToAppear ${this.navBarPageUrl}`)
    this.hiSearchPathInfo.clear();
    this.hiSearchPathInfo.pushPathByName(this.navBarPageUrl, { 'isFirst': true } as Record<string, boolean>, false);
  }

  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
    this.hiSearchPathInfo.clear();
  }

  @Builder
  naviPageMap(name: string, param: PageParam) {
    PcHiSearchRouterMap(name, param)
  }

  build() {
    Navigation(this.hiSearchPathInfo) {
    }
    .mode(NavigationMode.Stack)
    .hideNavBar(true)
    .navDestination(this.naviPageMap)
  }
}