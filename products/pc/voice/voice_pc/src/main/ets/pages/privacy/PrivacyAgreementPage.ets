/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger'
import { ProtocolIDConst } from '@hms-assistant/common-privacy/src/main/ets/config/StatementConfig';
import {
  STORAGE_KEY_APP_SETTING_PATH,
  STORAGE_KEY_BOTTOM_CUTOUT,
  STORAGE_KEY_IS_DARK_MODE,
  STORAGE_KEY_LEFT_CUTOUT,
  STORAGE_KEY_RIGHT_CUTOUT,
  STORAGE_KEY_SETTING_FROM_ABILITY,
  STORAGE_KEY_STATUS_BAR_HEIGHT,
} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { VoiceReport } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceReport';
import { VoiceConst } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceConst';
import { NavigationUtils } from '@hms-assistant/voice-phonebase/src/main/ets/util/NavigationUtils';
import { PafPrivacyStatement } from '@hms-paf/ui-widget-permission/src/main/ets/components/PafPrivacyStatement';
import { VoiceParam } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceParam';
import { AppProfile } from '@hms-assistant/common-corebase/src/main/ets/constant/AppProfile';
import { AbilityContextType } from '@hms-paf/ui-widget-base/src/main/ets/common/PafUiWidget';
import { StandardTagPaf } from '@hms-paf/ui-widget-base/src/main/ets/model/InAppItemModel';
import { SheetShowConfig } from '@hms-paf/ui-widget-base/src/main/ets/common/PafSheetWindow';
import { PafLifeCycle } from '@hms-paf/ui-widget-base/src/main/ets/common/PafNavPathRegister';
import { isInSplitNavigationMode } from '../setting/myPage/SettingNavigationConfig';
import lazy { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';

const TAG = 'PrivacyAgreementPage';

@Builder
export function PrivacyAgreementPageBuilder(name: string, param: Object) {
  PrivacyAgreementPage({
    agreementType: (param as Record<string, number>)?.agreementType,
    agreementTag: (param as Record<string, StandardTagPaf>)?.agreementTag,
  })
}

/**
 * 隐私相关web页面
 */
@Component
export struct PrivacyAgreementPage {
  sheetShowConfig?: SheetShowConfig = undefined;
  pafLife?: PafLifeCycle = undefined;
  isNav: boolean = true;
  hideTitleBar: boolean = false;
  @StorageProp(STORAGE_KEY_LEFT_CUTOUT) leftCutoutPx: number = 0;
  @StorageProp(STORAGE_KEY_RIGHT_CUTOUT) rightCutoutPx: number = 0;
  @StorageProp(STORAGE_KEY_BOTTOM_CUTOUT) bottomCutoutPx: number = 0;
  @StorageProp(STORAGE_KEY_STATUS_BAR_HEIGHT) statusBarHeightPx: number = 0;
  @StorageProp(STORAGE_KEY_SETTING_FROM_ABILITY) fromAbility: string = '';
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;
  @State localDarkMode: boolean = this.isDarkMode;
  agreementType: number = ProtocolIDConst.VOICE_ASSISTANT_SERVER_PRIVACY_STATEMENT_PC;
  agreementTag: StandardTagPaf = StandardTagPaf.DEFAULT;
  @State title: Resource = $r('app.string.third_share_list_string');

  aboutToAppear() {
    Logger.info(TAG, 'aboutToAppear');
    VoiceReport.setPageInfo(TAG, VoiceConst.PageStatus.FULL_SCREEN);
    this.reportDialogEnterReport();
    switch (this.agreementTag) {
      case StandardTagPaf.THIRD_SHARED:
        this.title = $r('app.string.third_share_list_string');
        break;
      case StandardTagPaf.DEFAULT:
        this.title = $r('app.string.privacy');
        break;
      case StandardTagPaf.PERSONAL_DATA:
        this.title =  $r('app.string.privacy_summary');
        break;
      default:
        break;
    }
  }

  private navigationEvent() {
    NavigationUtils.pop(STORAGE_KEY_APP_SETTING_PATH);
  }

  private reportDialogEnterReport(): void {
    let param: VoiceParam.DialogEnterParam = {
      option: VoiceConst.Option.DECLARE,
      enterType: VoiceConst.EnterType.APP,
      result: VoiceConst.Result.SUCCESS
    }
    VoiceReport.reportDialogEnter(param);
  }

  build() {
    PafPrivacyStatement({
      forceDarkAccess: false,
      agreementType: this.agreementType,
      agreementTag: this.agreementTag,
      customBackgroundColor: $r('sys.color.ohos_id_color_sub_background'),
      alwaysDarkMode: false,
      isNav: true,
      sheetShowConfig: this.sheetShowConfig,
      hideTitleBar: isInSplitNavigationMode() ? true : undefined,
      needShowNavigation: false,
      abilityContextType: AbilityContextType.WindowStage,
      externBrowserUrl: ResourceUtils.getResourceStringSync($r('app.string.externBrowserUrl')),
    }).id('privacy_agree_page.payload_builder.privacy_statement')
  }
}