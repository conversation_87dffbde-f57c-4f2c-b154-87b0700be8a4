/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import lazy { voiceSp } from '@hms-assistant/common-storage/src/main/ets/preference/VoicePreference';

@Observed
export class MeetingState {
  agentId: string = ''
  sessionId: string = ''
  dialogId: string = ''
  conversation: boolean = false
  liveMeeting: boolean = false
  showExtArea: boolean = false
  static dialogSessionMap: Map<string, string> = new Map()
  dialogPageIds: string[] = []

  constructor() {
    MeetingState.dialogSessionMap.clear()
  }

  initState(context?: Context, dialogId?: string, conversation?: boolean): void {
    this.dialogId = dialogId ?? ''
    this.conversation = conversation ?? false
    this.sessionId = this.getSession(this.dialogId)
    this.liveMeeting = Boolean(this.sessionId)
    this.showExtArea = false
    this.initDialogPageIds(context);
  }

  initDialogPageIds(context?: Context) {
    voiceSp.get(context, 'MeetingDialogPageIds', []).then((meetingDialogIds: string[]) => {
      this.dialogPageIds = meetingDialogIds
    })
  }

  getSession(dialogId: string): string {
    return MeetingState.dialogSessionMap.get(dialogId) ?? ''
  }

  getLiveSessionId(): string | undefined {
    let sessionIds = Array.from(MeetingState.dialogSessionMap.values())
    return sessionIds[0]
  }

  setSession(dialogId: string, sessionId: string) {
    MeetingState.dialogSessionMap.set(dialogId, sessionId)
  }

  clearSession(sessionId: string): string | undefined {
    if (this.sessionId === sessionId) {
      this.sessionId = ''
      this.liveMeeting = false
    }
    for (const entry of MeetingState.dialogSessionMap.entries()) {
      const dialogId = entry[0]
      const id = entry[1]
      if (id === sessionId) {
        MeetingState.dialogSessionMap.delete(dialogId)
        return dialogId
      }
    }
    return undefined
  }

  hasSession(dialogId: string): boolean {
    return MeetingState.dialogSessionMap.has(dialogId)
  }
}
