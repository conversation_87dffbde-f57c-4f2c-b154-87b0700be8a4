/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { AgentActivity } from '@hms-assistant/common-storage/src/main/ets/database/agentDataBean/AgentChipsPayload';
import MeetingConst from '@hms-assistant/common-corebase/src/main/ets/constant/MeetingConst';
import { RpcPeer } from '@vassistant/meeting/src/main/ets/extension/rpc/RpcPeer'
import lazy { STORAGE_KEY_CHAT_DATA_SOURCE } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { STORAGE_FULL_AGENT_INFO } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { eventHub } from '@hms-assistant/common-corebase/src/main/ets/eventbus/EventHub';
import lazy {
  MeetingExtensionParams
}  from '@vassistant/meeting/src/main/ets/ui/card/MeetingExtensionCard';
import { ChatRecord } from '@hms-assistant/common-storage/src/main/ets/database/ChatRecord';
import { CHIP, getChipText } from './Constant';
import { MeetingState } from './MeetingState';
import { MeetingCardPoster } from './MeetingCardPoster';
import { MeetingStub } from '../MeetingStub';
import { PcConstants } from '../../../common/PcConstants';
import { debounce } from '../../../utils/debounce';
import lazy { ChatDataSource } from '@vassistant/ui/src/main/ets/components/dataSource/ChatDataSource';
import lazy { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';

const TAG = `MeetingEventHandler`

export class MeetingEventHandler {
  private state: MeetingState
  private poster: MeetingCardPoster
  private rpcPeer: RpcPeer
  private stub: MeetingStub
  private uiContext: UIContext

  constructor(state: MeetingState, poster: MeetingCardPoster, rpcPeer: RpcPeer, stub: MeetingStub,
    uiContext: UIContext) {
    this.state = state;
    this.poster = poster;
    this.rpcPeer = rpcPeer
    this.stub = stub;
    this.uiContext = uiContext
  }


  registerAll(): void {
    eventHub.on(MeetingConst.EVENT.CHIPS_CLICK, this.handleChipClick)
    eventHub.on(PcConstants.CHAT_VIEW_ITEM_CHANGE_EVENT, this.handleChatViewItemChange)
  }

  private handleChatViewItemChange = debounce((range: VisibleListContentInfo[]) => {
    if (this.state.agentId !== this.getCurrAgentId()) {
      return
    }
    let start = range[0].index
    let end = range[1].index
    let dataSource = AppStorage.get<ChatDataSource>(STORAGE_KEY_CHAT_DATA_SOURCE)
    let hasSaveCard = false
    for (let i = start; i <= end; i++) {
      let record = dataSource?.getData(i)
      if (record?.content.includes(MeetingConst.PAGE.PostMeetingCard)) {
        hasSaveCard = true
        break
      }
    }
    Logger.info(TAG, `hasSaveCard: ${hasSaveCard}`)
  })
  private handleChipClick = async (data: AgentActivity) => {
    const content = data.parameters?.business
    let postHumanCard = async () => {
      let resourceStr = getChipText(content as CHIP)
      let str: string
      if (typeof resourceStr === 'string') {
        str = resourceStr
      } else {
        str = await this.uiContext.getHostContext()?.resourceManager?.getStringValue(resourceStr) ?? ''
      }
      this.poster.postHumanCard(ChatRecord.CARDNAME.USER_MSG, str, ChatRecord.SOURCE.USER)
    }
    if (content === CHIP.SegmentSummary) {
      const isNeedOpen = AppStorage.get<boolean>('isNeedOpenSegmentSummaryCard') ?? false
      await postHumanCard()
      if (isNeedOpen) {
        const cardContent: MeetingExtensionParams = {
          page: MeetingConst.PAGE.MeetingSegmentSummary,
          sessionId: this.state.sessionId,
          wantExtra: {
            'SummaryData': this.stub.summaryData
          }
        }
        this.poster.postSummaryCard(JSON.stringify(cardContent), ChatRecord.CARDNAME.MEETING_AGENT_SUMMARY_CARD);
        //同时下发完整摘要提示
        this.poster.postExtensionCard(this.state.sessionId, MeetingConst.PAGE.MeetingAllSegmentSummary,
          ChatRecord.CARDNAME.MEETING_EXTENSION_CARD, undefined, true);
      } else {
        let tips = ResourceUtils.getResourceStringSync($r('app.string.meeting_segment_summary_reach_limit'))
          .replace('%s', '200');
        this.poster.postHumanCard(ChatRecord.CARDNAME.XIAOYI_MSG, tips, ChatRecord.SOURCE.XIAOYI)
      }
    } else if (content === CHIP.SpeakerSummary) {
      const isNeedOpen = AppStorage.get<boolean>('isNeedOpenSpeakSummaryCard') ?? false
      if (isNeedOpen) {
        await postHumanCard()
        this.poster.postSummaryCard(this.stub.speakerSummaryData, ChatRecord.CARDNAME.MEETING_SPEAKER_SUMMARY_CARD)
      } else {
        this.rpcPeer.call(MeetingConst.RPC_METHOD.clickSmartHost, 'openFollowSpeakerDialog')
      }
    } else if (content === CHIP.SmartHost) {
      const isSmartHosting = AppStorage.get<boolean>('isSmartHosting') ?? false
      if (!isSmartHosting) {
        this.rpcPeer.call(MeetingConst.RPC_METHOD.clickSmartHost, 'openSmartHostDialog')
      }
    } else if (content === CHIP.CheckDetail) {
      this.rpcPeer.call(MeetingConst.RPC_METHOD.checkDetail, '')
    }
  }

  private getCurrAgentId(): string | undefined {
    return AppStorage.get<Record<string, string>>(STORAGE_FULL_AGENT_INFO)?.id
  }
}
