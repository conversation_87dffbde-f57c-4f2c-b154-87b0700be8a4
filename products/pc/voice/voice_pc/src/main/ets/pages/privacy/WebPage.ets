/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */
import webview from '@ohos.web.webview';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger'
import { PageParam } from '@hms-assistant/voice-phonebase/src/main/ets/util/NavigationUtils';
import { VaGridListContainer } from '@vassistant/ui/src/main/ets/layout/VaGridListContainer';
import { VaNavigation } from '@vassistant/ui/src/main/ets/layout/VaNavigation';
import { VoiceConst } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceConst';
import { VoiceReport} from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceReport';
import { VoiceParam } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceParam';
import { VoiceReportUtils } from '@hms-assistant/voice-phonebase/src/main/ets/util/VoiceReportUtils';
import { StatementType } from '@hms-assistant/common-privacy/src/main/ets/config/StatementConfig';

const TAG = 'WebPage';

@Builder
export function WebPageBuilder(name: string, param: PageParam) {
  WebPage({
    srcUri: param?.uri, statementType: param?.statementType
  })
}

/**
 * 隐私相关web页面
 */
@Component
export struct WebPage {
  controller: webview.WebviewController = new webview.WebviewController();
  srcUri: string = '';
  statementType?: StatementType;

  aboutToAppear() {
    Logger.info(TAG, 'aboutToAppear');
    VoiceReport.setPageInfo(TAG, VoiceConst.PageStatus.FULL_SCREEN);
    if (this.statementType === StatementType.PRIVACY_STATEMENT) {
      this.reportDialogEnterReport();
    }
  }

  onPageHide() {
    // 组件的onpagehide逻辑移到NavDestination的onHidden方法中
  }

  private reportDialogEnterReport(): void {
    let param: VoiceParam.DialogEnterParam = {
      option: VoiceConst.Option.DECLARE,
      enterType: VoiceConst.EnterType.APP,
      result: VoiceConst.Result.SUCCESS
    }
    VoiceReport.reportDialogEnter(param);
  }

  @Builder
  private contentView() {
    Column() {
      VaGridListContainer({ innerComponent: () => {
        this.webView();
      } });
    }
    .padding({
      bottom: $r('app.float.emui_default_margin')
    })
    .width('100%')
    .height('100%')
  }

  @Builder
  private webView() {
    Web({
      src: this.srcUri,
      controller: this.controller
    })
      .id('web_page.web_view.web')
      .fileAccess(false)
      .domStorageAccess(true)
      .javaScriptAccess(true)
      .forceDarkAccess(true)
      .darkMode(WebDarkMode.Auto)
      .geolocationAccess(false)
      .backgroundColor($r('sys.color.ohos_id_color_sub_background'))
  }

  build() {
    NavDestination() {
      Column() {
        VaNavigation({ innerComponent: () => {
          this.contentView();
        } });
      }
    }
    .hideTitleBar(true)
    .onShown(() => {
      Logger.info(TAG, 'onShown');
    })
    .onHidden(() => {
      Logger.info(TAG, 'onHidden');
      if (this.statementType === StatementType.PRIVACY_STATEMENT) {
        VoiceReport.reportDialogExit(VoiceConst.Option.DECLARE);
        VoiceReportUtils.setLagePageName(VoiceConst.LagePageName.WEB_PAGE);
      }
    })
  }
}