/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2023-2023. All rights reserved.
 */
import lazy {
  STORAGE_KEY_IS_BLUR_MODE,
  STORAGE_KEY_IS_SHOW_SIDE_BAR,
  STORAGE_KEY_IS_DARK_MODE,
  STORAGE_KEY_IS_SHOW_SCENARIO_PANEL,
  STORAGE_KEY_VOICE_LOAD_PAGE,
  STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE,
  STORAGE_KEY_VOICE_PATH,
  STORAGE_KEY_WINDOW_STATUS_TYPE,
  STORAGE_KEY_WINDOW_WIDTH,
  STORAGE_KEY_UNLOCK_MSG,
  STORAGE_DIALOG_PAGE_ID,
  STORAGE_DIALOG_PAGE_FROM_CONTINUE,
  STORAGE_AGENT_TYPE,
  STORAGE_DIALOG_PAGE_FROM_SIDEBAR,
  STORAGE_KEY_SYSTEM_FONT_SIZE_SCALE
} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import lazy { BIG_FONT_SIZE_SCALE_1X, FontSizeScaleUtils } from '@vassistant/ui/src/main/ets/utils/FontSizeScaleUtils';
import lazy { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import lazy { eventHub } from '@hms-assistant/common-corebase/src/main/ets/eventbus/EventHub';
import lazy { VaEmitterEvent } from '@hms-assistant/common-corebase/src/main/ets/eventbus/VaEmitterEvent';
import lazy { BaseUtils } from '@hms-assistant/common-corebase/src/main/ets/util/BaseUtils';
import lazy { AppProfile } from '@hms-assistant/common-corebase/src/main/ets/constant/AppProfile';
import lazy { ScreenLockUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ScreenLockUtils';
import lazy { NavigationUtils, PageParam } from '@hms-assistant/voice-phonebase/src/main/ets/util/NavigationUtils';
import lazy { BreakPoint, BREAKPOINT_KEY } from '@vassistant/ui/src/main/ets/utils/screen/ScreenConst';
import lazy { ScreenUtils } from '@vassistant/ui/src/main/ets/utils/screen/ScreenUtils';
import lazy { SafeJson } from '@hms-security/agoh-base-sdk';
import { common } from '@kit.AbilityKit';
import { PcAgentListBuilder } from '../agent/PcAgentListPage';
import { WindowAMenuType } from '@hms-assistant/common-storage';
import { window } from '@kit.ArkUI';
import { PcConstants } from '../../common/PcConstants';
import { PcWelcomePage } from '../privacy/PcWelcomePage';
import { PcScreenManager } from '../../utils/PcScreenManager';
import { MantleView } from '../chatpage/MantleView';
import { PcPinButton } from '../chatpage/PcPinButton';
import { i18n } from '@kit.LocalizationKit';
import { util } from '@kit.ArkTS';
import { AgentEntity } from '@hms-assistant/common-storage/src/main/ets/database/AgentEntity';
import { DialogPageRecord } from '@hms-assistant/common-storage/src/main/ets/database/DialogPageRecord'
import { HistoryMode, HistoryDataSource } from '@vassistant/ui/src/main/ets/components/dataSource/HistoryDataSource';
import lazy { AgentDialogListViewModel } from '@vassistant/ui/src/main/ets/pages/agent/AgentDialogListViewModel';
import { AgentModel } from '../dataSource/AgentDataSource';

const TAG = 'VoiceEntrancePage';

/**
 * 语音助手VoiceAbility界面主入口
 *
 * @since 2023-11-17
 */
@Entry({ useSharedStorage: true })
@Component
struct VoiceEntrancePage {
  @StorageProp(STORAGE_KEY_SYSTEM_FONT_SIZE_SCALE) fontSizeScale: number = BIG_FONT_SIZE_SCALE_1X;
  @StorageLink(STORAGE_KEY_VOICE_LOAD_PAGE) navBarPageUrl: string = '';
  @StorageLink(STORAGE_KEY_VOICE_PATH) voicePathInfo: NavPathStack = new NavPathStack();
  @StorageLink(STORAGE_KEY_IS_BLUR_MODE) isBlurMode: boolean = true;
  @StorageLink(STORAGE_KEY_IS_SHOW_SIDE_BAR) @Watch('isShowSideBarChange') isShowSideBar: boolean = false;
  @StorageLink(STORAGE_KEY_UNLOCK_MSG) @Watch('pcUnlockedChange') isPcUnlocked: boolean | undefined =
    !ScreenLockUtils.isLocked();
  @StorageProp(STORAGE_KEY_WINDOW_WIDTH) @Watch('onWindowWidthChange') windowWidth: number = 0;
  @StorageLink(BREAKPOINT_KEY) @Watch('currentBreakpointChange') currentBreakpoint: string = BreakPoint.SM;
  @StorageLink(STORAGE_KEY_WINDOW_STATUS_TYPE) windowStatusType: window.WindowStatusType =
    window.WindowStatusType.UNDEFINED;
  @StorageLink(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;
  @LocalStorageLink(PcConstants.NAV_MODE) navMode: NavigationMode = NavigationMode.Split
  @LocalStorageLink(PcConstants.NAV_BAR_STATE) navBarState: boolean = false
  @Provide currentAgentId: string = AgentEntity.XIAOYI_AGENT_ID
  @Provide currentAgentEntity: AgentEntity = AgentEntity.createXiaoyi()
  @Provide currentAgentType: number | undefined = undefined;
  @Provide currentDialogPage: HistoryMode | undefined = undefined;
  @Provide historyDataSource: HistoryDataSource = new HistoryDataSource();
  @Provide viewModel: AgentDialogListViewModel = new AgentDialogListViewModel();

  build() {
    Column() {
      if (this.navBarPageUrl === AppProfile.SubPage.WELCOME) {
        PcWelcomePage();
      }
      else if (this.navBarPageUrl === AppProfile.SubPage.CHAT_MAIN) {
        Row() {
          Row() {
            Image($r('app.media.icon'))
              .draggable(false)
              .width('24vp')
              .height('24vp')
              .borderRadius(6)

            Text($r('app.string.voice_assistant_name_new'))
              .fontSize(FontSizeScaleUtils.getMax1_45XFontSize(this.fontSizeScale,
                $r('sys.float.ohos_id_text_size_body1')))
              .fontColor($r('sys.color.font_primary'))
              .fontFamily('HarmonyHeiTi')
              .fontWeight(FontWeight.Medium)
              .alignSelf(ItemAlign.Center)
              .margin({
                left: 8
              })
          }
          .height('100%')
          .justifyContent(FlexAlign.Center)
          .alignItems(VerticalAlign.Center)
          .margin({
            left: 24
          })
        }
        .width('100%')
        .justifyContent(FlexAlign.SpaceBetween)
        .alignItems(VerticalAlign.Center)
        .height('40vp')
        .backgroundColor(this.isDarkMode ? '#FF404040' : '#FFE5E5EA')

        SideBarContainer(this.getSideBarType()) {
          Row() {
            //A区 侧边栏
            PcAgentListBuilder()
          }
          .backgroundColor(this.isDarkMode ? '#2E3033' : '#EEEEEE')

          //BC区 主界面
          this.HomeNavigation()
        }
        .height(`calc(100% - 40vp)`)
        .minContentWidth(362)
        .showControlButton(false)
        .showSideBar(this.isShowSideBar && this.isPcUnlocked)
        .sideBarWidth(240)
        .minSideBarWidth(240)
        .maxSideBarWidth(320)
        .autoHide(false)
        .divider({
          strokeWidth: '1px',
          color: '#33000000',
        })
        .responseRegion({
          x: '-400%',
          y: 0,
          width: '800%',
          height: '100%'
        })
        .onChange((value: boolean) => {
          Logger.info(TAG, 'SideBarContainer onChange status:' + value);
        })
      }
      else if (this.navBarPageUrl === AppProfile.SubPage.PC_SIDEBAR_PAGE) {
        Row() {
          Row() {
            PcAgentListBuilder()
          }
          .visibility(Visibility.None)
          this.HomeNavigation()
        }
       .height('100%')
      }
    }
    .width('100%')
    .height('100%')
  }

  @Builder
  HomeNavigation() {
    Stack() {
        Column() {
          Column() {
            Navigation(this.voicePathInfo) {
            }
            .hideToolBar(true)
            .hideBackButton(true)
            .hideTitleBar(true)
            .hideNavBar(true)
            .mode(NavigationMode.Stack)
            .backgroundColor(Color.Transparent)
            .keyboardShortcut("w", [ModifierKey.CTRL], () => {
              (getContext(this) as common.UIAbilityContext)?.terminateSelf();
            })
            .onNavBarStateChange((visible) => {
              this.navBarState = visible
            })
            .onNavigationModeChange((mode) => {
              Logger.info(TAG, `mode:${mode}`);
              this.navMode = mode
            })
          }
          .width('100%')
          .height('100%')
        }
        .width('100%')
        .height('100%')

      //蒙层
      MantleView()
    }
    .backgroundColor(this.isDarkMode ? '#2E3033' : '#EEEEEE')
  }

  private getSideBarType(): SideBarContainerType {
    if (this.currentBreakpoint === BreakPoint.LG || this.currentBreakpoint === BreakPoint.MD) {
      return SideBarContainerType.Embed;
    } else {
      return SideBarContainerType.Overlay;
    }
  }

  currentBreakpointChange(){
    Logger.info(TAG, `currentBreakpoint:${this.currentBreakpoint}`);
  }

  onWindowWidthChange() {
    let width = px2vp(this.windowWidth)
    Logger.info(TAG, `onWindowWidthChange width:${width}`);
    PcScreenManager.updateBreakPointByWindWidth(width);
  }

  private isShowSideBarChange(): void {
    Logger.info(TAG, `isShowSideBarChange isShowSideBar:${this.isShowSideBar}`);
  }

  private pcUnlockedChange(): void {
    Logger.info(TAG, `pcUnlockedChange isPcUnlocked:${this.isPcUnlocked}`);
  }


  aboutToAppear() {
    Logger.info(TAG, `aboutToAppear ${this.navBarPageUrl}`)
    eventHub.on(VaEmitterEvent.SELECTION_PC_MINI_PANEL, (messageData: Record<string, HistoryMode>) => {
      this.selectPanelHandler(messageData)
    })
      AppStorage.setOrCreate<string>(STORAGE_DIALOG_PAGE_ID, util.generateRandomUUID())
      AppStorage.setOrCreate(STORAGE_AGENT_TYPE, WindowAMenuType.CHAT_AGENT)
    if (AppStorage.get<window.WindowStatusType>(STORAGE_KEY_WINDOW_STATUS_TYPE) ===
    window.WindowStatusType.SPLIT_SCREEN) {
      Logger.info(TAG, `WINDOW_STATUS_TYPE SPLIT_SCREEN`)
      this.isShowSideBar = false;
    }
    if (this.navBarPageUrl === AppProfile.SubPage.CHAT_MAIN || this.navBarPageUrl === AppProfile.SubPage.PC_SIDEBAR_PAGE) {
      this.voicePathInfo.pushPathByName(AppProfile.SubPage.CHAT_MAIN_PC, new PageParam(), false)
    }
  }


  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
    this.isShowSideBar = false;
    AppStorage.setOrCreate(STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE, false);
    eventHub.off(VaEmitterEvent.SELECTION_PC_MINI_PANEL, (messageData: Record<string, HistoryMode>) => {
      this.selectPanelHandler(messageData)
    })
  }

  onBackPress(): boolean | void {
    if (AppStorage.get<boolean>(STORAGE_KEY_IS_SHOW_SCENARIO_PANEL)) {
      AppStorage.setOrCreate(STORAGE_KEY_IS_SHOW_SCENARIO_PANEL, false)
      return true;
    } else {
      return false;
    }
  }

  onPageShow() {
    Logger.debug(TAG, `${SafeJson.ohAegJsonStringify(NavigationUtils.getAllPathName(STORAGE_KEY_VOICE_PATH))}`);
    let local = LocalStorage.getShared()
    let key = 'key_params'
    let parameters = local?.get(key) as Record<string, string>;
    if (parameters?.launch_type === 'openExtArea') {
      let dialogPageId = parameters?.dialogPageId
      let agentId = AgentEntity.XIAOYI_AGENT_ID;
      let userId = parameters?.userId
      let openExtDialog = new HistoryMode(new DialogPageRecord().create(dialogPageId, agentId, userId))
      this.miniToChat(openExtDialog)
    }
    let continueMiniDialog = AppStorage.get<HistoryMode>(STORAGE_DIALOG_PAGE_FROM_CONTINUE);
    let dialogFromSideBar = AppStorage.get<HistoryMode>(STORAGE_DIALOG_PAGE_FROM_SIDEBAR)
    if (continueMiniDialog) {
      this.miniToChat(continueMiniDialog)
      AppStorage.delete(STORAGE_DIALOG_PAGE_FROM_CONTINUE);
    }
    if (dialogFromSideBar) {
      let continueDialogSideBar =
        new HistoryMode(new DialogPageRecord().create(dialogFromSideBar?.dialogPageId, dialogFromSideBar?.agentId,
          dialogFromSideBar?.creatorId, dialogFromSideBar?.latestWord, dialogFromSideBar?.updateTime))
      this.miniToChat(continueDialogSideBar)
      AppStorage.delete(STORAGE_DIALOG_PAGE_FROM_SIDEBAR);
    }
    if (BaseUtils.isEmptyArr(NavigationUtils.getAllPathName(STORAGE_KEY_VOICE_PATH)) === false) {
      Logger.info(TAG, 'NavBar not on the top');
      return;
    }
    Logger.info(TAG, 'onPageShow');
    AppStorage.setOrCreate(STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE, true);
  }

  onPageHide() {
    Logger.debug(TAG, `${SafeJson.ohAegJsonStringify(NavigationUtils.getAllPathName(STORAGE_KEY_VOICE_PATH))}`);
    if (BaseUtils.isEmptyArr(NavigationUtils.getAllPathName(STORAGE_KEY_VOICE_PATH)) === false) {
      Logger.info(TAG, 'NavBar not on the top');
      return;
    }
    Logger.info(TAG, 'onPageHide');
    AppStorage.setOrCreate(STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE, false);
  }

  // 定义新的回调并保存引用
  private selectPanelHandler = (messageData: Record<string, HistoryMode>) => {
    let continueDialog = messageData?.miniContinue as HistoryMode
    this.miniToChat(continueDialog)
  };


  private miniToChat(continueDialog: HistoryMode) {
    AppStorage.setOrCreate<string>(STORAGE_DIALOG_PAGE_ID, continueDialog.dialogPageId)
    AppStorage.setOrCreate(STORAGE_AGENT_TYPE, WindowAMenuType.HISTORY_AGENT)
    this.historyDataSource.newDialog(continueDialog)
    this.currentDialogPage = continueDialog
    this.currentAgentType = WindowAMenuType.HISTORY_AGENT
    let pageName = this.currentAgentEntity?.extendInfo?.pcLayout?.layoutType ?? 'PcChatPage';
    NavigationUtils.replace(STORAGE_KEY_VOICE_PATH, pageName, {
      agentInfo: this.currentAgentEntity as AgentModel,
      isHistoryDialog: true,
      dialogPageRecord: continueDialog as DialogPageRecord
    } as ESObject, false);
  }
}
