/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import { TimbreCard } from '@vassistant/ui/src/main/ets/layout/VaTimbreContainer';

export const timbreSelectListen: Map<string, string> = new Map([
  ['0', '你好，我是你的智慧助手'],
  ['1', '有需要随时呼唤我'],
  ['2', '吃完蛋糕玩一下乐高'],
  ['3', '有什么我可以帮你的']
]);

export const officiallyTimbre: Array<TimbreCard> = [
  new TimbreCard($r('app.media.female_voice_light'), $r('app.media.female_voice_dark'),
    $r('app.string.female_voice'), $r('app.string.female_voice_describe'), '0', true, 100,  $rawfile('timbre/female_voice.mp4'), $rawfile('timbre/female_voice_dark.mp4')),
  new TimbreCard($r('app.media.child_voice_light'), $r('app.media.child_voice_dark'),
    $r('app.string.child_voice'), $r('app.string.child_voice_describe'), '2', true, 100, $rawfile('timbre/child_voice.mp4'), $rawfile('timbre/child_voice_dark.mp4')),
  new TimbreCard($r('app.media.male_voice_light'), $r('app.media.male_voice_dark'),
    $r('app.string.male_voice'), $r('app.string.male_voice_describe'), '3', true, 100, $rawfile('timbre/male_voice.mp4'), $rawfile('timbre/male_voice_dark.mp4')),
  new TimbreCard($r('app.media.maid_voice_light'), $r('app.media.maid_voice_dark'),
    $r('app.string.maid_voice'), $r('app.string.maid_voice_describe'), '1', true, 100, $rawfile('timbre/girl_voice.mp4'), $rawfile('timbre/girl_voice_dark.mp4'))
]

export { TimbreCard };
