import Settings from '@ohos.settings';
import { BaseUtils } from '@hms-assistant/common-corebase/src/main/ets/util/BaseUtils';
import common from '@ohos.app.ability.common';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';

export const KEY_BAR_TYPE = 'settings_in_status_bar_type';
/**
 * 主类型，mac风格状态栏
 */
export const TYPE_MAIN_MAC_STATUS_BAR: number = 0x0080;

/**
 * 主类型，window类型状态栏
 */
export const TYPE_MAIN_WINDOW_STATUS_BAR: number = 0x0800;

const TAG = 'CapsulePluginUtils'

export default class CapsulePluginUtils{
  public static readStatusBarType(context: common.Context): number {
    let barType: string = Settings.getValueSync(context, KEY_BAR_TYPE, '', Settings.domainName.USER_SECURITY);;
    Logger.info(TAG, `readStatusBarType barType ${barType}`);
    if (BaseUtils.isEmptyObj(barType)) {
      return TYPE_MAIN_MAC_STATUS_BAR
    }
    let barTypeNumber = Number(barType);
    if(barTypeNumber === TYPE_MAIN_WINDOW_STATUS_BAR) {
      return TYPE_MAIN_WINDOW_STATUS_BAR;
    }
    return TYPE_MAIN_MAC_STATUS_BAR;
  }
}