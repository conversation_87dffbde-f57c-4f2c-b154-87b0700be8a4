/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import lazy { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { BaseUtils } from '@hms-assistant/common-aikitbase/Index';
import lazy { ShowContent } from '@hms-assistant/voice-voicebusiness/src/main/ets/model/cv/markdownsave/MarkdownFileBean';
import { common, wantConstant } from '@kit.AbilityKit';
import { DocBean } from '@hms-assistant/voice-phonebase/src/main/ets/bean/doc/DocBean';
import { STORAGE_KEY_IS_DARK_MODE } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';

const TAG = `MarkdownSaveShowDialog`
@Component
export struct MarkdownSaveShowDialog {
  @Link visible: boolean;
  @Link @Watch('refreshText') showText: string;
  @State text: string = '';
  private docBean: DocBean | undefined;
  @State isShowOpen: boolean = false;
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;
  private timeId = 0;

  aboutToAppear() {
    Logger.info(TAG, 'aboutToAppear');
  }

  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
  }

  private refreshText() {
    Logger.info(TAG, `refreshText`)
    Logger.debug(TAG, `show text ${this.showText}`)
    this.isShowOpen = false;
    if (BaseUtils.isEmptyStr(this.showText)) {
      return;
    }
    let showTextObject = JSON.parse(this.showText) as ShowContent;
    if (BaseUtils.isEmptyObj(showTextObject)) {
      return;
    }
    this.text = showTextObject.text;
    if (BaseUtils.isEmptyStr(this.text)) {
      return;
    }
    let time = 3000;
    this.visible = true;
    this.docBean = showTextObject.doc;
    let fileUrl = this.docBean?.fileUrl;
    if (!BaseUtils.isEmptyObj(this.docBean) && !BaseUtils.isEmptyStr(fileUrl)) {
      this.isShowOpen = true;
      time = 5000;
    }
    Logger.info(TAG, `isShowOpen ${this.isShowOpen}`)
    clearTimeout(this.timeId);
    this.timeId = setTimeout(() => {
      this.visible = false;
      this.isShowOpen = false;
      this.text = '';
      this.docBean = undefined;
    }, time)
  }

  build() {
    Column() {
      Row() {
        Text(`${this.text}`)
          .fontSize('14')
          .fontFamily('HarmonyHeiTi')
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .fontWeight(FontWeight.Regular)
          .margin({
            left: $r('app.float.prompt_vp_8'),
            right: $r('app.float.prompt_vp_8'),
            top: $r('app.float.prompt_vp_8'),
            bottom: $r('app.float.prompt_vp_8')
          })
          .textOverflow({ overflow: TextOverflow.Ellipsis });
        Text(`打开`)
          .fontSize('14')
          .fontFamily('HarmonyHeiTi')
          .fontColor($r('sys.color.font_emphasize'))
          .fontWeight(FontWeight.Medium)
          .margin({
            left: $r('app.float.prompt_vp_8'),
            right: $r('app.float.prompt_vp_8'),
            top: $r('app.float.prompt_vp_8'),
            bottom: $r('app.float.prompt_vp_8')
          }).onClick(() => {
            Logger.info(TAG, `do open`);
            this.openFolder(this.docBean);
        }).visibility(this.isShowOpen ? Visibility.Visible: Visibility.None)
          .textOverflow({ overflow: TextOverflow.Ellipsis });
      }
    }.height(36)
    .backgroundColor(this.isDarkMode ? '#66000000' : $r('sys.color.ohos_id_blur_style_component_regular_color'))
    .borderRadius(8)
    .border({
        color : this.isDarkMode ? '#33ffffff' : '#1A000000',
        width: '1vp'
    })
    .shadow(ShadowStyle.OUTER_DEFAULT_MD)
    .alignItems(HorizontalAlign.Center)
    .backgroundBlurStyle(BlurStyle.COMPONENT_ULTRA_THICK)
  }

  private openFolder(doc: DocBean | undefined) {
    if (!doc) {
      Logger.warn(TAG, `do open error`);
      return;
    }
    let context = getContext(this) as common.UIAbilityContext;

    let want: Want = {
      action: "ohos.want.action.viewData",
      abilityName: 'MainAbility',
      bundleName: 'com.huawei.hmos.hipreview',
      type: doc.docType,
      uri: doc.fileUrl,
      flags: wantConstant.Flags.FLAG_AUTH_READ_URI_PERMISSION,
      parameters: {
        title: doc.fileName,
        fileName: { 'name': doc.fileName },
        uri: doc.fileUrl,
      }
    }
    context.startAbility(want)
  }
}

