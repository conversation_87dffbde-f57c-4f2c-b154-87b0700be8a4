/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

import { STORAGE_KEY_IS_DARK_MODE } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { BCSplitExtAreaToolbar } from '@vassistant/ui/src/main/ets/components/bcContainer/BCSplitExtAreaToolbar';
import { BCSplitController } from '@vassistant/ui/src/main/ets/components/bcContainer/BCSplitController';
import { MeetingExtension, } from '@vassistant/meeting/src/main/ets/ui/card/MeetingExtension';
import MeetingConst from '@hms-assistant/common-corebase/src/main/ets/constant/MeetingConst';
import { MeetingStub } from './MeetingStub';

@Builder
export function MeetingExtAreaBuilder(name: string, param: Record<string, Object>) {
  MeetingExtArea({
    sessionId: param?.sessionId as string
  })
}

@Component
export struct MeetingExtArea {
  sessionId?: string
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;
  @Consume('bcSplitController') bcSplitController: BCSplitController;

  aboutToDisappear(): void {
    MeetingStub.getInstance().onExtAreaClose()
  }

  build() {
    NavDestination() {
      Stack() {
        MeetingExtension({
          page: MeetingConst.PAGE.TranscribePanelCard,
          sessionId: this.sessionId
        })
        BCSplitExtAreaToolbar({
          onClose: () => {
            MeetingStub.getInstance().onExtAreaClose()
          }
        })
      }
      .alignContent(Alignment.Top)
      .padding({
        left: 24,
        right: 24,
        bottom: 16
      })
    }
    .backgroundColor(Color.Transparent)
  }
}
