/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import MeetingConst from '@hms-assistant/common-corebase/src/main/ets/constant/MeetingConst';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';


const TAG = `MeetingInitializer`

export class MeetingInitializer {
  private initPromise?: Promise<void>
  private initResolve?: () => void
  private storage: LocalStorage

  constructor(storage: LocalStorage) {
    this.storage = storage;
  }

  public async run(): Promise<void> {
    if (this.storage.get(MeetingConst.STORE_KEY.RPC_PEER)) {
      return
    }
    if (this.initPromise) {
      await this.initPromise
      return
    }

    this.initPromise = new Promise(resolve => {
      this.initResolve = resolve
      Logger.info(TAG, 'Waiting for RPC Peer')
      this.storage.setOrCreate(MeetingConst.STORE_KEY.KEEP_ALIVE, true)
    })

    await this.initPromise
    this.initPromise = undefined
  }

  public resolve(): void {
    this.initResolve?.()
  }
}
