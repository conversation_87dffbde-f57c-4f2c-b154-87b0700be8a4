/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2022. All rights reserved.
 */

import connection from '@ohos.net.connection';
import common from '@ohos.app.ability.common';
import display from '@ohos.display';
import { AbilityUtils } from '@hms-assistant/voice-voicebusiness/src/main/ets/utils/AbilityUtils';
import { BusinessError } from '@ohos.base';
import { PageParam } from '@hms-assistant/voice-phonebase/src/main/ets/util/NavigationUtils';
import { VaNavigation } from '@vassistant/ui/src/main/ets/layout/VaNavigation'
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';
import { AppProfile } from '@hms-assistant/common-corebase/src/main/ets/constant/AppProfile';
import { StatementType } from '@hms-assistant/common-privacy/src/main/ets/config/StatementConfig';

const TAG = 'NetworkUnavailablePage';

@Builder
export function NetworkUnavailablePageBuilder(name: string, param: PageParam) {
  NetworkUnavailablePage({
    srcUri: param.uri,
    statementType: param?.statementType,
    pathStack: param?.pathStack
  })
}

@Component
export struct NetworkUnavailablePage {
  @State minWindowSize: number = 0;
  srcUri: string = '';
  statementType?: StatementType;
  pathStack?: NavPathStack;
  private readonly DEFAULT_WINDOW_SIZE: number = 600;

  aboutToAppear() {
    Logger.info(TAG, 'aboutToAppear');
    let displayInfo = display.getDefaultDisplaySync();
    this.minWindowSize = Math.min(px2vp(displayInfo.width), px2vp(displayInfo.height));
    Logger.debug(TAG, 'minWindowSize ' + this.minWindowSize);
  }

  onPageShow() {
  }

  private refreshPage(): void {
    Logger.info(TAG, 'refreshPage');
    connection.hasDefaultNet().then((result: boolean) => {
      Logger.info(TAG, 'network is activated = ' + result);
      if (result) {
        this.pathStack?.replacePathByName(AppProfile.SubPage.WEB, {
          uri: this.srcUri,
          statementType: this.statementType,
          pathStack: this.pathStack
        } as PageParam)
      }
    }).catch((err: BusinessError) => {
      Logger.error(TAG, `catch error, ${err.code}, ${err.message}`);
    });
  }

  @Builder
  private contentView() {
    RelativeContainer() {
      Button(ResourceUtils.getResourceStringSync($r('app.string.setting_network')).toUpperCase())
        .id('network_unavailable.content_view.button')
        .onClick(() => {
          let context = getContext(this) as common.UIAbilityContext;
          AbilityUtils.setNetwork(context);
        })
        .backgroundColor($r('sys.color.ohos_id_color_button_normal'))
        .type(ButtonType.Capsule)
        .fontColor($r('sys.color.ohos_id_color_text_primary_activated'))
        .id('bottomButton_id')
        .alignRules({
          middle: { anchor: '__container__', align: HorizontalAlign.Center },
          bottom: { anchor: '__container__', align: VerticalAlign.Bottom }
        })
        .padding({ left: 36, right: 36 })
        .margin({ bottom: 24 });

      Flex({
        direction: FlexDirection.Column,
        alignItems: ItemAlign.Center,
        justifyContent: FlexAlign.Center
      }) {
        Image($r('app.media.no_network'))
          .id('network_unavailable.content_view.image')
          .draggable(false)
          .width(this.minWindowSize < this.DEFAULT_WINDOW_SIZE ? 120 : 160)
          .height(this.minWindowSize < this.DEFAULT_WINDOW_SIZE ? 120 : 160).onClick(() => {
          this.refreshPage();
        })

        Text($r('app.string.network_not_connect'))
          .id('network_unavailable.content_view.text')
          .fontSize($r('app.float.privacy_text_size_body1'))
          .fontColor($r('sys.color.ohos_id_color_secondary'))
          .onClick(() => {
            this.refreshPage();
          })
          .margin({ top: 8 })
      }
      .width('100%')
      .alignRules({
        top: { anchor: '__container__', align: VerticalAlign.Top },
        bottom: { anchor: 'bottomButton_id', align: VerticalAlign.Top },
      })
      .id('center')
    }.width('100%').height('100%')
  }

  build() {
    NavDestination() {
      Stack() {
        VaNavigation({ innerComponent: () => {
          this.contentView();
        } });
      }
    }
    .hideTitleBar(true)
    .onShown(() => {
      Logger.info(TAG, 'onPageShow');
      // 设置网络并回退到未联网页面进行刷新
      this.refreshPage();
    })
    .onHidden(() => {
      Logger.info(TAG, 'onPageHide');
    })
  }
}