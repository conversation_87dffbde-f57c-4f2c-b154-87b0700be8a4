/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { ChatRecord } from '@hms-assistant/common-storage/src/main/ets/database/ChatRecord';
import { STORAGE_KEY_ACCOUNT_UID } from '@hms-assistant/common-storage';

interface ChatRecordParams {
  user?: string,
  cardName?: string,
  source?: number,
  content?: string,
  agentId?: string,
  notDialogScene?: string,
  dialogPageId?: string
}

function createRecord({
  user,
  cardName,
  source,
  content,
  agentId,
  notDialogScene,
  dialogPageId
}: ChatRecordParams): ChatRecord {
  return ChatRecord.create(
    user ?? AppStorage.get<string>(STORAGE_KEY_ACCOUNT_UID),
    cardName ?? '',
    source ?? ChatRecord.SOURCE.NONE,
    content ?? '',
    agentId,
    notDialogScene,
    dialogPageId
  );
}

export default {
  createRecord
}