/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import MeetingConst from '@hms-assistant/common-corebase/src/main/ets/constant/MeetingConst';
import { RpcPeer } from '@vassistant/meeting/src/main/ets/extension/rpc/RpcPeer'
import { UIContext } from '@kit.ArkUI';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';
import { AgentStartUpPayload } from '@hms-assistant/voice-actions/src/main/ets/bean/appcontroller/AgentStartUpPayload';
import {
  AgentActivity,
  AgentActivityParameters
} from '@hms-assistant/common-storage/src/main/ets/database/agentDataBean/AgentChipsPayload';
import { MeetingState } from './internal/MeetingState';
import { MeetingCardPoster } from './internal/MeetingCardPoster';
import { MeetingUIManager } from './internal/MeetingUiManager';
import { MeetingInitializer } from './internal/MeetingInitializer';
import { MeetingEventHandler } from './internal/MeetingEventHandler';
import { MeetingRpcHandler } from './internal/MeetingRpcHandler';
import { CHIP, getChipText, STARTUP_ACTION } from './internal/Constant';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import lazy { ChatPageViewModel } from '../chatpage/ChatPageViewModel';
import lazy { MeetingFloatBarBuilder } from './MeetingFloatBar';
import lazy { voiceSp } from '@hms-assistant/common-storage/src/main/ets/preference/VoicePreference';

const TAG = `MeetingStub`

@Observed
export class MeetingStub {
  private static instance?: MeetingStub

  public static getInstance(): MeetingStub {
    if (!MeetingStub.instance) {
      MeetingStub.instance = new MeetingStub()
    }
    return MeetingStub.instance
  }

  @Track
  state = new MeetingState()
  @Track
  chips: AgentActivity[] = [
    CHIP.SegmentSummary,
    CHIP.SpeakerSummary,
    CHIP.SmartHost
  ].map((key, i) => {
    let obj = new AgentActivity()
    obj.type = `meetingChipButton`
    obj.content = getChipText(key) as string
    obj.parameters = new AgentActivityParameters()
    obj.parameters.business = key
    return obj
  })
  @Track
  historyChips: AgentActivity[] = [
    CHIP.CheckDetail
  ].map((key, i) => {
    let obj = new AgentActivity()
    obj.type = `meetingChipButton`
    obj.content = getChipText(key) as string
    obj.parameters = new AgentActivityParameters()
    obj.parameters.business = key
    return obj
  })

  private storage?: LocalStorage
  private uiContext?: UIContext
  private dialogBuilder?: () => void
  private context?: Context
  private poster = new MeetingCardPoster(this.state)
  private uiManager?: MeetingUIManager
  private initializer?: MeetingInitializer
  private eventHandler?: MeetingEventHandler
  private rpcHandler?: MeetingRpcHandler
  summaryData: string = '';
  allSummaryData: string = '';
  speakerSummaryData: string = '';

  public setStorage(storage: LocalStorage, context: Context): void {
    Logger.info(TAG, `setStorage`)
    this.storage = storage
    this.context = context
    this.initializer = new MeetingInitializer(storage)
    //首次加载同步备忘录历史信息
    this.checkNotePad();
  }

  public onRpcPeerInit(rpcPeer: RpcPeer, uiContext: UIContext, customBuilder: () => void): void {
    Logger.info(TAG, `onRpcPeerInit`)
    this.uiContext = uiContext
    this.dialogBuilder = customBuilder

    this.uiManager = new MeetingUIManager(uiContext, customBuilder, this.state)
    this.poster = new MeetingCardPoster(this.state)
    this.eventHandler = new MeetingEventHandler(this.state, this.poster, rpcPeer, this, uiContext)
    this.rpcHandler = new MeetingRpcHandler({
      rpcPeer,
      state: this.state,
      poster: this.poster,
      uiManager: this.uiManager,
      stub: this,
      uiContext: uiContext
    })

    this.eventHandler.registerAll()
    this.rpcHandler.registerAll()

    if (this.initializer) {
      this.initializer.resolve()
    }
  }

  public async init(params: AgentStartUpPayload | undefined, dialogId: string | undefined,
    conversation: boolean | undefined,
    chatPageViewModel: ChatPageViewModel): Promise<void> {
    Logger.info(TAG, `init`)
    chatPageViewModel.setChatViewHeaderBuilder(MeetingFloatBarBuilder)
    this.state.initState(this.context, dialogId, conversation)

    await this.initializer!.run()

    let action = params?.extra?.action
    if (action === STARTUP_ACTION.START || action === STARTUP_ACTION.STOP) {
      setTimeout(async () => {
        let sessionId = this.state.getLiveSessionId()
        if (sessionId && this.uiManager) {
          if (action === STARTUP_ACTION.STOP) {
            const rpcPeer = this.storage!.get<RpcPeer>(MeetingConst.STORE_KEY.RPC_PEER)!
            rpcPeer.call(MeetingConst.RPC_METHOD.prepareTerminate, [sessionId, true])
          }
        }
      }, 100)
    }
  }

  public onExtAreaClose(): void {
    if (this.state.showExtArea === false) {
      return
    }
    Logger.info(TAG, `onExtAreaClose`)
    this.state.showExtArea = false
  }

  public getAgentId(): string {
    if (!this.state.agentId) {
      this.state.agentId = ResourceUtils.getResStringSync('meeting_agent_id')
    }
    return this.state.agentId
  }

  public addDialogPageId(dialogPageId: string) {
    voiceSp.get(this.context, 'MeetingDialogPageIds', []).then((meetingDialogIds: string[]) => {
      let existIndex: number = meetingDialogIds.findIndex(obj => obj === dialogPageId);
      if (existIndex === -1) {
        meetingDialogIds.push(dialogPageId)
        this.state.dialogPageIds.push(dialogPageId);
        voiceSp.set(this.context, 'MeetingDialogPageIds', meetingDialogIds)
      }
    });
  }

  public checkDialogPageId(): boolean {
    let existIndex: number = this.state.dialogPageIds.findIndex(obj => obj === this.state.dialogId);
    return existIndex !== -1
  }

  public getContext(): Context | undefined {
    return this.context
  }

  private async checkNotePad() {
    let isCheckNotePad: boolean = await voiceSp.get(this.context, 'isCheckNotePad', false)
    if (isCheckNotePad) {
      Logger.info(TAG, 'no need check notePad')
    } else {
      AppStorage.setOrCreate('isShowNotePadSync', true);
      await this.initializer!.run()
    }
  }
}
