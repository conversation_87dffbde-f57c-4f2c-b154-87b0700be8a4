/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import lazy {
  MeetingExtensionParams
}  from '@vassistant/meeting/src/main/ets/ui/card/MeetingExtensionCard';
import { eventHub } from '@hms-assistant/common-corebase/src/main/ets/eventbus/EventHub';
import { ChatRecord } from '@hms-assistant/common-storage/src/main/ets/database/ChatRecord';
import { AppUiMessage, AppUiMessageData } from '@hms-assistant/common-corebase/src/main/ets/constant/UiMessage';
import { JSON } from '@kit.ArkTS';
import lazy { ChatDb } from '@hms-assistant/common-storage/src/main/ets/database/ChatDb';
import MeetingUtil from './MeetingUtil';
import { MeetingState } from './MeetingState';
import { STORAGE_DIALOG_PAGE_ID } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';

export class MeetingCardPoster {
  constructor(state: MeetingState) {
    this.state = state;
  }

  private state: MeetingState

  postHumanCard(cardName: string, humanMsg: string, source: number) {
    let record = MeetingUtil.createRecord({
      cardName: cardName,
      content: humanMsg,
      source: source,
      agentId: this.state.agentId
    })
    record.cardType = ChatRecord.CARDTYPE.TEXT
    this.postCard(record, true, AppUiMessage.CardType.HUMAN_CARD)
  }

  postExtensionCard(sessionId: string, page: string, cardName: string,
    wantExtra?: Record<string, string>, persist: boolean = false, dialogId?: string): ChatRecord {
    const cardContent: MeetingExtensionParams = {
      page,
      sessionId,
      wantExtra
    }
    const record = MeetingUtil.createRecord({
      agentId: this.state.agentId,
      cardName,
      dialogPageId: dialogId ?? this.state.dialogId,
      content: JSON.stringify(cardContent)
    })
    this.postCard(record, persist)
    return record
  }

  postSummaryCard(cardContent: string, cardName: string): ChatRecord {
    const summaryRecord = MeetingUtil.createRecord({
      cardName,
      agentId: this.state.agentId,
      dialogPageId: this.state.dialogId,
      content: cardContent
    })
    summaryRecord.cardType = ChatRecord.CARDTYPE.VIEW_HISTORY
    this.postCard(summaryRecord, true)
    return summaryRecord
  }

  postCard(record: ChatRecord, persist: boolean = false, messageName: string = record.cardName) {
    if (record.dialogPageId === AppStorage.get<string>(STORAGE_DIALOG_PAGE_ID)) {
      eventHub.post(AppUiMessage.UiDisplay.CARD_DISPLAY_FOR_CHAT_VIEW,
        new AppUiMessageData(messageName, this.state.agentId, record))
    }
    if (persist) {
      ChatDb.getInstance().insert(record)
    }
  }

  insertExtensionCard(sessionId: string, page: string, cardName: string, dialogPageId: string,
    wantExtra?: Record<string, string>): void {
    const cardContent: MeetingExtensionParams = {
      page,
      sessionId,
      wantExtra
    }
    const record = MeetingUtil.createRecord({
      cardName,
      dialogPageId: dialogPageId,
      content: JSON.stringify(cardContent),
      agentId: this.state.agentId
    })
    ChatDb.getInstance().insert(record)
  }
}