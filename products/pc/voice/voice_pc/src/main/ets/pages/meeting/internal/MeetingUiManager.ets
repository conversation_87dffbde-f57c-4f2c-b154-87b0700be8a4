/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { UIContext } from '@kit.ArkUI';
import { getCurrentController } from '@vassistant/ui/src/main/ets/components/bcContainer/BCSplitController';
import { MeetingState } from './MeetingState';


export class MeetingUIManager {
  private uiContext: UIContext
  private dialogBuilder: () => void
  private state: MeetingState

  constructor(uiContext: UIContext, dialogBuilder: () => void, state: MeetingState) {
    this.uiContext = uiContext
    this.dialogBuilder = dialogBuilder
    this.state = state
  }

  async openExtArea(sessionId: string): Promise<void> {
    const controller = getCurrentController()
    if (controller?.isShowExtArea() || !sessionId) {
      return
    }
    this.state.showExtArea = true
    await controller?.openExtArea({
      name: 'MeetingExtArea',
      params: { sessionId }
    })
  }

  openDialog(sessionId: string = ''): Promise<number> {
    const promptAction = this.uiContext.getPromptAction()
    return promptAction.openCustomDialog({
      builder: this.dialogBuilder?.bind(null, sessionId),
      onWillDismiss: () => {},
      width: '100%',
      height: '100%',
      cornerRadius: 0,
      borderWidth: 0,
      maskColor: Color.Transparent,
      shadow: { color: Color.Transparent, radius: 0 },
      isModal: false,
      backgroundBlurStyle: BlurStyle.NONE
    }).then((id) => {
      AppStorage.setOrCreate('meetingExtensionDialogId', id)
      return id
    })
  }

  closeDialog(): void {
    const id = AppStorage.get('meetingExtensionDialogId') as number
    this.uiContext.getPromptAction().closeCustomDialog(id)
  }
}
