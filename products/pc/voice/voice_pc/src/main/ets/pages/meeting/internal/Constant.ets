/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */

export enum CHIP {
  SegmentSummary = 'SegmentSummary',
  SpeakerSummary = 'SpeakerSummary',
  SmartHost = 'SmartHost',
  CheckDetail = 'CheckDetail',
}

export function getChipText(chip: CHIP): ResourceStr {
  switch (chip) {
    case CHIP.SegmentSummary:
      return $r('app.string.meeting_segment_summary_title')
    case CHIP.SpeakerSummary:
      return $r('app.string.speech_summary')
    case CHIP.SmartHost:
      return $r('app.string.smart_host_title')
    case CHIP.CheckDetail:
      return $r('app.string.check_detail_title')
    default:
      return ''
  }
}

export enum STARTUP_ACTION {
  START = 'start',
  STOP = 'stop'
}
