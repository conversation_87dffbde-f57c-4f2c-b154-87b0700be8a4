import common from '@ohos.app.ability.common';
import Want from '@ohos.app.ability.Want';
import StartOptions from '@ohos.app.ability.StartOptions';
import display from '@ohos.display';
import CapsulePluginUtils, { TYPE_MAIN_WINDOW_STATUS_BAR } from './CapsulePluginUtils';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { PcConstants } from '../../common/PcConstants';
import { PcStatusBarType } from '../chatpage/PcStatusBarType';
import { BusinessError } from '@ohos.base';
import { AbilityConstant } from '@kit.AbilityKit';
import { i18n } from '@kit.LocalizationKit';
import { SettingUtils } from '@hms-assistant/voice-voicebusiness/src/main/ets/utils/SettingUtils';
import systemParameterEnhance from '@ohos.systemParameterEnhance';

class StyleRule {
  public hoverColor?: Color | string;
  public clickColor?: Color | string;
  public transparentColor?: Color | string;
}

const BUNDLE_NAME: string = 'com.huawei.hmos.vassistant';
const ABILITY_PC: string = 'VoicePcAbility'
const PLUGIN_CLICK: string = 'Plguin';
const ELEMENT_NAME: string = '18';
const CLICK_TYPE: string = '2';
const TAG: string = 'PluginXiaoyi';
const PAGE_STATUS: string = '4';
const FOLD_SCREEN_FLAG: String = systemParameterEnhance.getSync('const.window.foldscreen.type','0,0,0,0');
const WINDOW_WIDTH_INIT: number = 602;
const WINDOW_HEIGHT_INIT: number = 536;

const style: StyleRule = {
  hoverColor: '#0C000000',
  clickColor: '#19000000',
  transparentColor: Color.Transparent,
};

const HOVER_ANIMATION_SCALE = 1.15;
const CLICK_ANIMATION_SCALE = 0.9;
const NORMAL_SCALE = 1;
const ITERATIONS = 1;
const ANIMATION_DURATION_TIME = 200;
const TOP_DOCK_HEIGHT: number = 40;
const DOCK_HEIGHT: number = 65;
const MIDDLE_SIGN: number = 45.55;
const WINDOW_LEFT_MARGIN: number =
  FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER ? PcConstants.HPR_WINDOW_LEFT_MARGIN : PcConstants.HAD_WINDOW_LEFT_MARGIN;
const WINDOW_TOP_OFFSET: number =
  FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER ? PcConstants.HPR_WINDOW_TOP_OFFSET : PcConstants.HAD_WINDOW_TOP_OFFSET;
const MAC_TOP_MARGIN: number =
  FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER ? PcConstants.HPR_MAC_TOP_MARGIN : PcConstants.HAD_MAC_TOP_MARGIN

@Entry
@Component
struct PluginXiaoyi {
  @State isIconHover: boolean | undefined = false;
  @State isClicked: boolean = false;
  @State scaleX: number = 1;
  @State scaleY: number = 1;
  @State imgRes: Resource = $r('app.media.xiaoyi_icon_no_shadow_45');
  @State statusBarType: number = 0x0080;
  @State screenHeight: number = vp2px(PcConstants.WINDOW_HEIGHT_FINAL);
  hoverScaleEnable: boolean = true;
  clickScaleEnable: boolean = true;

  aboutToAppear(): void {
    Logger.info(TAG, `aboutToAppear`);
    this.updateImgBySetting();
  }

  private updateImgBySetting() {
    this.imgRes = $r('app.media.xiaoyi_icon_no_shadow');
    this.hoverScaleEnable = false;
    this.isIconHover = false;
    this.clickScaleEnable = false;
  }

  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
  }

  onBackgroundColorChange(): ResourceColor {
    if (this.isClicked) {
      return style.clickColor as ResourceColor;
    }
    if (this.isIconHover) {
      return style.hoverColor as ResourceColor;
    }
    return style.transparentColor as ResourceColor;
  }

  onIconClick() {
    if (this.clickScaleEnable) {
      animateTo({
        duration: ANIMATION_DURATION_TIME,
        curve: Curve.Sharp,
        iterations: ITERATIONS,
        playMode: PlayMode.Normal,
        onFinish: () => {
          this.isClicked = false;
          this.scaleX = HOVER_ANIMATION_SCALE;
          this.scaleY = HOVER_ANIMATION_SCALE;
        }
      }, () => {
        this.isClicked = true;
        this.scaleX = CLICK_ANIMATION_SCALE;
        this.scaleY = CLICK_ANIMATION_SCALE;
      })
    }
    let voicePCWant: Want = {
      bundleName: BUNDLE_NAME,
      abilityName: ABILITY_PC,
      parameters: {
        launch_type: PLUGIN_CLICK,
        elementName: ELEMENT_NAME,
        clickType: CLICK_TYPE,
        pageName: TAG,
        pageStatus: PAGE_STATUS
      }
    }
    let displayInfo: display.Display = display.getDefaultDisplaySync();
    let promise = displayInfo.getAvailableArea();
    this.statusBarType = PcStatusBarType.readStatusBarType(getContext() as common.UIAbilityContext);
    let isMacStyle = PcStatusBarType.isMacStyle(this.statusBarType)
    promise.then((data) => {
      this.screenHeight = data.height;
      Logger.info(TAG, 'xiaoyi')
      let offsetLeft: number =
        i18n.isRTL(i18n.System.getAppPreferredLanguage()) ? WINDOW_LEFT_MARGIN :
          displayInfo.width - vp2px(WINDOW_WIDTH_INIT) - WINDOW_LEFT_MARGIN;
      let height: number = isMacStyle ?
        (this.screenHeight - WINDOW_TOP_OFFSET - MAC_TOP_MARGIN) :
        this.screenHeight - 2 * WINDOW_TOP_OFFSET
      let offsetTop: number =
        isMacStyle ? PcConstants.HAD_TOP_DOCK_HEIGHT + MAC_TOP_MARGIN : WINDOW_TOP_OFFSET;
      let reduceHeight: number = isMacStyle ?
        vp2px(DOCK_HEIGHT) + WINDOW_TOP_OFFSET +
          MAC_TOP_MARGIN +
        vp2px(TOP_DOCK_HEIGHT) :
        vp2px(DOCK_HEIGHT) + WINDOW_TOP_OFFSET +  WINDOW_TOP_OFFSET
      if (FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER) {
        if (display.getFoldStatus() === display.FoldStatus.FOLD_STATUS_EXPANDED) {
          height = (displayInfo.orientation === display.Orientation.PORTRAIT ||
            displayInfo.orientation === display.Orientation.PORTRAIT_INVERTED) ?
            displayInfo.height / 2 - reduceHeight - vp2px(MIDDLE_SIGN / 2) :
            displayInfo.width / 2 - reduceHeight - vp2px(MIDDLE_SIGN / 2)
          offsetTop = displayInfo.height - vp2px(DOCK_HEIGHT) - WINDOW_TOP_OFFSET - height;
        } else {
          height = displayInfo.height - reduceHeight
          offsetTop = isMacStyle ? vp2px(TOP_DOCK_HEIGHT) + MAC_TOP_MARGIN : WINDOW_TOP_OFFSET;
        }
      }
      let voicePCOptions: StartOptions = {
        windowLeft: offsetLeft,
        windowTop: offsetTop,
        windowWidth: vp2px(WINDOW_WIDTH_INIT),
        windowHeight: height
      }
      if (SettingUtils.isSplitScreenEnableSync(getContext()) && FOLD_SCREEN_FLAG !== PcConstants.IS_HOPPER) {
        voicePCOptions.windowMode = AbilityConstant.WindowMode.WINDOW_MODE_SPLIT_SECONDARY
      }
      let context = getContext() as common.ServiceExtensionContext;
      context.startAbility(voicePCWant, voicePCOptions);
    }).catch((err: BusinessError) => {

    })
  }

  hoverAnimation(isHover: boolean) {
    Logger.info(TAG, `hoverAnimation ${isHover}`);
    if (isHover) {
      animateTo({
        duration: ANIMATION_DURATION_TIME,
        curve: Curve.Friction,
        iterations: ITERATIONS,
        playMode: PlayMode.Normal,
      }, () => {
        this.scaleX = HOVER_ANIMATION_SCALE;
        this.scaleY = HOVER_ANIMATION_SCALE;
      })
    } else {
      animateTo({
        duration: ANIMATION_DURATION_TIME,
        curve: Curve.Friction,
        iterations: ITERATIONS,
        playMode: PlayMode.Normal,
      }, () => {
        this.scaleX = NORMAL_SCALE;
        this.scaleY = NORMAL_SCALE;
      })
    }
  }

  build() {
    Column() {
      Stack() {
        Row()
          .width(40)
          .height(40)
          .borderRadius(8)
          .backgroundColor(this.onBackgroundColorChange())

        Image(this.imgRes)
          .id('plugin_xiaoyi.build.image')
          .draggable(false)
          .scale({ x: this.scaleX, y: this.scaleY })
          .width(32)
          .height(32)
          .objectFit(ImageFit.Contain)
          .autoResize(false)
          .interpolation(ImageInterpolation.Medium)
      }
      .onHover((isHover) => {
        if (!this.hoverScaleEnable) {
          return;
        }
        this.isIconHover = isHover;
        this.hoverAnimation(isHover);
      })
    }
    .width('100%')
    .height('100%')
    .alignItems(HorizontalAlign.Center)
    .justifyContent(FlexAlign.Center)
    .onClick(() => {
      this.onIconClick()
    });
  }
}