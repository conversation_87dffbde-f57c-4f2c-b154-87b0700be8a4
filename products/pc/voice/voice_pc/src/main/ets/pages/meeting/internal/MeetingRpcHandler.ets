/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import MeetingConst from '@hms-assistant/common-corebase/src/main/ets/constant/MeetingConst';
import { RpcPeer } from '@vassistant/meeting/src/main/ets/extension/rpc/RpcPeer'
import { eventHub } from '@hms-assistant/common-corebase/src/main/ets/eventbus/EventHub';
import lazy { VaEmitterEvent } from '@hms-assistant/common-corebase/src/main/ets/eventbus/VaEmitterEvent';
import { getCurrentController } from '@vassistant/ui/src/main/ets/components/bcContainer/BCSplitController';
import { ChatRecord } from '@hms-assistant/common-storage/src/main/ets/database/ChatRecord';
import common from '@ohos.app.ability.common';
import { PcConstants } from '../../../common/PcConstants';
import { MeetingState } from './MeetingState';
import { MeetingCardPoster } from './MeetingCardPoster';
import { MeetingUIManager } from './MeetingUiManager';
import { MeetingStub } from '../MeetingStub';
import { agentService } from '../../chatpage/AgentService';
import MeetingUtil from './MeetingUtil';
import calendarManager from '@ohos.calendarManager'
import { BusinessError } from '@ohos.base';
import lazy { voiceSp } from '@hms-assistant/common-storage/src/main/ets/preference/VoicePreference';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';
import lazy {
  MeetingExtensionParams
}  from '@vassistant/meeting/src/main/ets/ui/card/MeetingExtensionCard';

interface MeetingRpcDeps {
  rpcPeer: RpcPeer
  state: MeetingState
  poster: MeetingCardPoster
  uiManager: MeetingUIManager
  stub: MeetingStub
  uiContext: UIContext
}

const TAG = `MeetingRpcHandler`

export class MeetingRpcHandler {
  constructor(deps: MeetingRpcDeps) {
    this.deps = deps;
  }

  private deps: MeetingRpcDeps

  registerAll(): void {
    const rpcPeer = this.deps.rpcPeer

    rpcPeer.register(MeetingConst.RPC_METHOD.initDialog, (paramSessionId?: string) => {
      let sessionId = paramSessionId
      let state = this.deps.state
      if (!sessionId) {
        sessionId = state.sessionId
      }
      if (!sessionId) {
        sessionId = state.getLiveSessionId()
      }
      return this.deps.uiManager.openDialog(sessionId)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.closeDialog, () => {
      return this.deps.uiManager.closeDialog()
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.startMeeting, async (params: Record<string, string>) => {
      let state = this.deps.state
      state.sessionId = params.sessionId ?? ''
      state.liveMeeting = !params?.history
      if (state.liveMeeting) {
        if (!state.hasSession(this.deps.state.dialogId)) {
          eventHub.post(VaEmitterEvent.PC_CLEAR_DIALOG, true)
          state.setSession(state.dialogId, state.sessionId)
          let meetingTitle = params?.meetingTitle
          this.addHistoryDialog(meetingTitle)
        }
      } else {
        if (!state.conversation) {
          Logger.info(TAG, `conversation`)
        }
      }
      state.conversation = true
      state.showExtArea = true
      await this.deps.uiManager.openExtArea(state.sessionId)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.stopMeeting, async (params: Record<string, string | boolean>) => {
      if (params?.beforeSave) {
        getCurrentController()?.closeExtArea()
        this.deps.stub.onExtAreaClose()
        return
      }
      let dialogId = this.deps.state.clearSession(params.sessionId as string)
      //chips按钮替换
      AppStorage.setOrCreate('isReSetMeetingChips', true);
      if (params?.history) {
        return
      }
      AppStorage.delete('isSmartHosting');
      AppStorage.delete('isNeedOpenSpeakSummaryCard');
      AppStorage.delete('isNeedOpenSegmentSummaryCard');
      let meetingTitle = params?.meetingTitle as string
      this.updateHistoryDialog(meetingTitle)
      if (params?.forceStop) {
        //区分直接结束智能体、关闭自由窗
        if (params?.isMeetingSaveBtnClick) {
          getCurrentController()?.closeExtArea()
          this.deps.stub.onExtAreaClose()
        } else {
          (this.deps.uiContext.getHostContext() as common.UIAbilityContext).terminateSelf()
        }
      } else {
        getCurrentController()?.closeExtArea()
        this.deps.stub.onExtAreaClose()
        if (params.saveInfo) {
          this.deps.poster.postExtensionCard('', MeetingConst.PAGE.PostMeetingCard,
            ChatRecord.CARDNAME.MEETING_EXTENSION_CARD, { 'postMeetingCardInfo': params.saveInfo as string }, true,
            dialogId)
          //更新查看详情的历史对话
          this.deps.stub.addDialogPageId(dialogId!)
        }
      }
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.toggleWindow, (value: boolean) => {
      return (this.deps.uiContext.getHostContext() as common.UIAbilityContext).windowStage.getMainWindow()
        .then(window => {
          window.setSnapshotSkip(value)
        })
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.changeSmartHostStatus, (value: boolean) => {
      AppStorage.setOrCreate('isSmartHosting', value)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.receiveSpeakSummary, (params: Record<string, string>) => {
      if (params.speakerSummaryData) {
        this.deps.stub.speakerSummaryData = params.speakerSummaryData
      }
      AppStorage.setOrCreate('isNeedOpenSpeakSummaryCard', params.isNeedOpenSpeakSummaryCard)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.receiveSegmentSummary, (params: Record<string, string>) => {
      if (params.summaryData && params.allSummaryData) {
        this.deps.stub.summaryData = params.summaryData
        this.deps.stub.allSummaryData = params.allSummaryData
      }
      AppStorage.setOrCreate('isNeedOpenSegmentSummaryCard', params.isNeedOpenSegmentSummaryCard)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.isCheckNotePad, (value: boolean) => {
      voiceSp.set(this.deps.stub.getContext()!, 'isCheckNotePad', value).then(() => {
        AppStorage.setOrCreate('isShowNotePadSync', false);
      })
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.jumpToHistoryMeeting, (params: Record<string, string>) => {
      this.changeHistoryDialog(params.dialogId, params.title, params.uuid)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.queryAllSummaryData, (params: Record<string, string>) => {
      let tips = ResourceUtils.getResourceStringSync($r('app.string.full_summary_content'))
      this.deps.poster.postHumanCard(ChatRecord.CARDNAME.USER_MSG, tips, ChatRecord.SOURCE.USER)
      const cardContent: MeetingExtensionParams = {
        page: MeetingConst.PAGE.MeetingAllSegmentSummary,
        sessionId: this.deps.state.sessionId,
        wantExtra: {
          'AllSummaryData': this.deps.stub.allSummaryData
        }
      }
      this.deps.poster.postSummaryCard(JSON.stringify(cardContent), ChatRecord.CARDNAME.MEETING_AGENT_SUMMARY_CARD)
    })

    rpcPeer.register(MeetingConst.RPC_METHOD.importMeetingFormNotePad,
      async (params: Record<string, string | boolean>) => {
        if (!params?.history) {
          let meetingTitle = params?.meetingTitle as string
          let dialogPageId = params?.dialogPageId as string
          this.updateHistoryDialog(meetingTitle)
          this.deps.poster.insertExtensionCard('', MeetingConst.PAGE.PostMeetingCard,
            ChatRecord.CARDNAME.MEETING_EXTENSION_CARD, dialogPageId,
            { 'postMeetingCardInfo': params.saveInfo as string })
          //更新查看详情的历史对话
          this.deps.stub.addDialogPageId(dialogPageId!)
        }
      })

    rpcPeer.register(MeetingConst.RPC_METHOD.addScheduleStub, (params: Record<string, string>) => {
      let event: string = params?.event;
      if (event !== undefined && event !== '') {
        let calendarManagerEvent: calendarManager.Event = JSON.parse(event)
        let calendarMgr = calendarManager.getCalendarManager(getContext(this) as common.UIAbilityContext)
        calendarMgr?.editEvent(calendarManagerEvent).then((eventId: number) => {
          Logger.info(TAG, `add event result:` + eventId);
        }).catch((e: BusinessError) => {
          Logger.error(TAG, `addScheduleStub error ${JSON.stringify(e)}`)
        })
      }
    })

    eventHub.post(PcConstants.TERMINATE_HANDLER_REGISTER_EVENT, [TAG, () => {
      let state = this.deps.state
      let sessionId = state.liveMeeting ? state.sessionId : state.getLiveSessionId()
      if (sessionId) {
        rpcPeer.call(MeetingConst.RPC_METHOD.prepareTerminate, [sessionId])
        return true
      }
      return false
    }])
  }

  private changeHistoryDialog(dialogId: string, title: string, uuid: string) {
    let record = MeetingUtil.createRecord({
      dialogPageId: dialogId,
      agentId: this.deps.state.agentId,
    })
    eventHub.post(PcConstants.ADD_UPDATE_HISTORY_DIALOG_EVENT, {
      type: 'change',
      data: [record, this.deps.state.agentId, title, uuid]
    } as PcConstants.AddUpdateHistoryDialogParam)
  }

  private updateHistoryDialog(title: string) {
    let record = MeetingUtil.createRecord({
      dialogPageId: this.deps.state.dialogId,
      agentId: this.deps.state.agentId,
    })
    eventHub.post(PcConstants.ADD_UPDATE_HISTORY_DIALOG_EVENT, {
      type: 'update',
      data: [record, this.deps.state.agentId, title]
    } as PcConstants.AddUpdateHistoryDialogParam)
  }

  private addHistoryDialog(title?: string) {
    let recommendListData = agentService.fetchAgentRecommendListFromLocal(this.deps.state.agentId)
    let record = MeetingUtil.createRecord({
      cardName: ChatRecord.CARDNAME.AGENT_RECOMMEND_LIST_CARD,
      content: recommendListData,
      agentId: this.deps.state.agentId,
      dialogPageId: this.deps.state.dialogId
    })
    eventHub.post(PcConstants.ADD_UPDATE_HISTORY_DIALOG_EVENT, {
      type: 'add',
      data: [record, this.deps.state.agentId, title]
    } as PcConstants.AddUpdateHistoryDialogParam)
    this.deps.poster.postCard(record, true)
  }
}
