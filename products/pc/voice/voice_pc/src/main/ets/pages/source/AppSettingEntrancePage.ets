/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE, } from '@hms-assistant/common-storage';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { BaseUtils } from '@hms-assistant/common-corebase/src/main/ets/util/BaseUtils';
import { NavigationUtils, PageParam } from '@vassistant/ui';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import { PcRouterMap } from '../PcRouterMap';
import {
  STORAGE_KEY_APP_SETTING_PATH,
  STORAGE_KEY_SETTING_LOAD_PAGE,
  STORAGE_KEY_WINDOW_EVENT_TYPE
} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import common from '@ohos.app.ability.common';
import { window } from '@kit.ArkUI';
import { Builder, MainNavPageRegistry } from '@hms-paf/ui-widget-base';
import { InitParams, WiseSupportService, WiseSupportSheet } from '@hms-wisesupport/wisesupport';

const TAG = 'AppSettingEntrancePage';

let localStorage = LocalStorage.getShared();

/**
 * 语音助手settingAbility界面主入口
 *
 * @since 2023-11-17
 */
@Entry(localStorage)
@Component
struct AppSettingEntrancePage {
  @StorageLink(STORAGE_KEY_SETTING_LOAD_PAGE) navBarPageUrl: string = '';
  @StorageProp(STORAGE_KEY_WINDOW_EVENT_TYPE) @Watch('commonWindowEventModeChange') commonWindowEventMode: window.WindowEventType =
    window.WindowEventType.WINDOW_SHOWN;
  settingPathInfo: NavPathStack = new NavPathStack();
  @BuilderParam extensionNavPageBuilder?: Builder

  aboutToAppear() {
    Logger.debug(TAG, `aboutToAppear ${this.navBarPageUrl}`)
    this.settingPathInfo.pushPathByName(this.navBarPageUrl, { 'isFirst': true } as Record<string, boolean>, false);
    AppStorage.setOrCreate<NavPathStack>('MyHomeStack', this.settingPathInfo);
  }

  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
  }

  onBackPress(): boolean | void {
    Logger.info(TAG, 'onBackPress');
  }

  onPageShow() {
    Logger.debug(TAG, `${SafeJson.ohAegJsonStringify(NavigationUtils.getAllPathName(STORAGE_KEY_APP_SETTING_PATH))}`);
    if (BaseUtils.isEmptyArr(NavigationUtils.getAllPathName(STORAGE_KEY_APP_SETTING_PATH)) === false) {
      Logger.info(TAG, 'NavBar not on the top');
      return;
    }
    Logger.info(TAG, 'onPageShow');
  }

  onPageHide() {
    Logger.debug(TAG, `${SafeJson.ohAegJsonStringify(NavigationUtils.getAllPathName(STORAGE_KEY_APP_SETTING_PATH))}`);
  }

  @Builder
  naviPageMap(name: string, param: object) {
    if (this.changeDynamicBuilder(name, param)) {
      this.BuildNavDestinationExtension(param as InitParams);
    } else {
      PcRouterMap(name, param as PageParam);
    }
  }

  changeDynamicBuilder(navPath: string, param: object): boolean {
    const builder: Builder | undefined = MainNavPageRegistry.getMainNavPageRegistry()!.getNavPageBuilder(navPath);
    if (!builder) {
      return false;
    }
    this.extensionNavPageBuilder = builder;
    return true;
  }

  @Builder
  BuildNavDestinationExtension(param: InitParams) {
    if (this.extensionNavPageBuilder) {
      this.extensionNavPageBuilder(param)
    }
  }

  build() {
    Navigation(this.settingPathInfo) {
    }
    .mode(NavigationMode.Stack)
    .navDestination(this.naviPageMap)
    .hideNavBar(true)
    .onNavBarStateChange((isVisible: boolean) => {
      if (isVisible) {
        Logger.info(TAG, 'onNavBarStateChange show');
      } else {
        Logger.info(TAG, 'onNavBarStateChange hide');
      }
    })
    .keyboardShortcut('w', [ModifierKey.CTRL], () => {
      (getContext(this) as common.UIAbilityContext).terminateSelf();
    })
  }

  commonWindowEventModeChange(changedPropertyName: string) {
    if (this.commonWindowEventMode === window.WindowEventType.WINDOW_DESTROYED) {
      (getContext(this) as common.UIAbilityContext).terminateSelf();
    }
  }
}