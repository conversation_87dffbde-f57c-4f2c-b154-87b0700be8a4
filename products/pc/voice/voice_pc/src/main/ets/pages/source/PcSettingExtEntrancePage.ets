/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import UIExtensionContentSession from '@ohos.app.ability.UIExtensionContentSession';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import { PcRouterMap } from '../PcRouterMap';
import lazy {
  STORAGE_KEY_SETTING_LOAD_PAGE,
  STORAGE_KEY_SETTING_PATH
} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { NavigationUtils, PageParam } from '@hms-assistant/voice-phonebase/src/main/ets/util/NavigationUtils';
const TAG = 'PcSettingExtEntrancePage';
const COLOR_TRANSPARENT = '#00000000';
let localStorage = LocalStorage.getShared();

/**
 * 系统设置SettingAbility界面主入口
 *
 * @since 2023-11-17
 */
@Entry(localStorage)
@Component
struct PcSettingExtEntrancePage {
  @StorageLink(STORAGE_KEY_SETTING_LOAD_PAGE) navBarPageUrl: string = '';
  @StorageLink(STORAGE_KEY_SETTING_PATH) settingPathInfo: NavPathStack = new NavPathStack()
  @LocalStorageLink('session') session: UIExtensionContentSession | undefined = undefined;
  // oobe权限弹窗
  @LocalStorageLink('oobeInfo') oobeInfo: Record<string, string | boolean> = {};

  aboutToAppear() {
    Logger.debug(TAG, `aboutToAppear ${this.navBarPageUrl}`);
    NavigationUtils.push(STORAGE_KEY_SETTING_PATH, this.navBarPageUrl, { 'isFirst': true } as Record<string, boolean>,
      false);
    // OOBE添加透明背景色
    if (this.oobeInfo?.isOobe) {
      this.session?.setWindowBackgroundColor(COLOR_TRANSPARENT);
    }
  }

  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
  }

  onBackPress(): boolean | void {
    Logger.info(TAG, 'onBackPress');
  }

  onPageShow() {
    Logger.debug(TAG, `onPageShow ${SafeJson.ohAegJsonStringify(
      NavigationUtils.getAllPathName(STORAGE_KEY_SETTING_PATH))}`);
  }

  onPageHide() {
    Logger.debug(TAG, `onPageHide ${SafeJson.ohAegJsonStringify(
      NavigationUtils.getAllPathName(STORAGE_KEY_SETTING_PATH))}`);
  }

  @Builder
  naviPageMap(name: string, param: PageParam) {
    PcRouterMap(name, param)
  }

  build() {
    Navigation(this.settingPathInfo) {
    }
    .mode(NavigationMode.Stack)
    .hideNavBar(true)
    .navDestination(this.naviPageMap)
    .onNavBarStateChange((isVisible: boolean) => {
      // 此方法只有根页面和相邻跳转的子页面之间切换才会触发
      if (isVisible) {
        Logger.info(TAG, 'onNavBarStateChange show');
      } else {
        Logger.info(TAG, 'onNavBarStateChange hide');
      }
    })
  }
}