import { STORAGE_KEY_IS_DARK_MODE } from '@hms-assistant/common-storage';
import { ReportParamNew, VoiceReportNew } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceReportNew';
import { VoiceReport, VoiceReportCodeMapping } from '@hms-assistant/voice-phonebase';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import { PcConstants } from '../../common/PcConstants';
import systemParameterEnhance from '@ohos.systemParameterEnhance';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';
import { Logger } from '@hms-paf/ui-widget-base';

const FOLD_SCREEN_FLAG: String = systemParameterEnhance.getSync('const.window.foldscreen.type', '0,0,0,0');
const TAG = 'BeginnerGuideDialog'

class MyDataSource implements IDataSource {
  private data: MyData[] = []

  constructor(data: MyData[]) {
    this.data = data;
  }

  totalCount(): number {
    return this.data.length
  }

  getData(index: number): MyData {
    return this.data[index]
  }

  registerDataChangeListener(listener: DataChangeListener): void {
  }

  unregisterDataChangeListener() {
  }
}

class MyData {
  private image: string = '';
  private text1: string = '';
  private text2: string = '';
  private button: string = '';

  constructor(image: string, text1: string, text2: string, button: string) {
    this.image = image;
    this.text1 = text1;
    this.text2 = text2;
    this.button = button;
  }

  getImage(): string {
    return this.image;
  }

  getText1(): string {
    return this.text1;
  }

  getText2(): string {
    return this.text2;
  }

  getButton(): string {
    return this.button;
  }
}

@CustomDialog
@Component
export struct BeginnerGuideDialog {
  private beginnerGuideDialog: CustomDialogController;
  private swiperController: SwiperController = new SwiperController();
  private data: MyDataSource = new MyDataSource([]);
  @StorageLink(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;
  @State index: number = 0;

  aboutToAppear(): void {
    let isHopper: boolean = FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER
    let data: MyData[] = [];
    if (!this.isDarkMode) {
      data[0] = new MyData(isHopper ? 'app.media.guide_icon_one_hpr_light' : 'app.media.guide_icon_one_light', 'app.string.beginner_guide_click_icon',
        'app.string.beginner_guide_text_directive', 'app.string.beginner_guide_button_next')
      data[1] = new MyData(isHopper ? 'app.media.guide_icon_two_hpr_light' : 'app.media.guide_icon_two_light', 'app.string.beginner_guide_click_smart_key',
        'app.string.beginner_guide_text_directive', 'app.string.beginner_guide_button_next')
      data[2] = new MyData(isHopper ? 'app.media.guide_icon_two_hpr_light' : 'app.media.guide_icon_two_light', 'app.string.beginner_guide_press_smart_key',
        'app.string.beginner_guide_voice_directive_two', 'app.string.beginner_guide_button_next')
      data[3] = new MyData(isHopper ? 'app.media.guide_icon_three_hpr_light' : 'app.media.guide_icon_three_light', 'app.string.beginner_guide_speak_celia',
        'app.string.beginner_guide_voice_directive', 'app.string.beginner_guide_know')
    } else {
      data[0] = new MyData(isHopper ? 'app.media.guide_icon_one_hpr_dark' : 'app.media.guide_icon_one_dark', 'app.string.beginner_guide_click_icon',
        'app.string.beginner_guide_text_directive', 'app.string.beginner_guide_button_next')
      data[1] = new MyData(isHopper ? 'app.media.guide_icon_two_hpr_dark' : 'app.media.guide_icon_two_dark', 'app.string.beginner_guide_click_smart_key',
        'app.string.beginner_guide_text_directive', 'app.string.beginner_guide_button_next')
      data[2] = new MyData(isHopper ? 'app.media.guide_icon_two_hpr_dark' : 'app.media.guide_icon_two_dark', 'app.string.beginner_guide_press_smart_key',
        'app.string.beginner_guide_voice_directive_two', 'app.string.beginner_guide_button_next')
      data[3] = new MyData(isHopper ? 'app.media.guide_icon_three_hpr_dark' : 'app.media.guide_icon_three_dark', 'app.string.beginner_guide_speak_celia',
        'app.string.beginner_guide_voice_directive', 'app.string.beginner_guide_know')
    }
    this.data = new MyDataSource(data)
    VoiceReportNew.reportContentExposure(this.getExposureInfoForReport(0));
  }

  /**
   * 为感应区构建曝光类型（5005）的打点信息
   * @returns 打点信息
   */
  getExposureInfoForReport(index: number): ReportParamNew {
    let text = '';
    switch (index) {
      case 0:
        text = '点击状态栏小艺图标.唤起小艺窗口，可直接输入文本指令'
        break;
      case 1:
        text = '点击智慧键.唤起小艺窗口，可直接输入文本指令'
        break;
      case 2:
        text = '长按智慧键.唤起小艺窗口，可直接进行语音对话'
        break;
      case 3:
        text = '说出“小艺小艺”.开启语音唤醒后，直接唤起小艺进行对话'
        break;
      default :
        break;
    }
    let cArray: ReportParamNew[] = [
      {
        mt: VoiceReportCodeMapping.ModuleType.beginnerGuidance,
        ct: '版本引导卡片',
        pos: index + 1,
        cid: 'new_user_guide',
        text: text
      }
    ];
    let reportParamNew: ReportParamNew = {
      p: VoiceReportCodeMapping.PageName.xiaoyiChatView,
      ps: VoiceReportCodeMapping.PageStatus.pcWindow,
      tp: VoiceReportCodeMapping.ActionType.exposure,
      c: SafeJson.ohAegJsonStringifyArray(cArray),
    };
    return reportParamNew;
  }

  /**
   * 为关闭感应区构建点击类型（5006）的打点信息
   * @returns 打点信息
   */
  getClickInfoForReport(pos: number, aid: string): ReportParamNew {
    let reportParamNew: ReportParamNew = {
      p: VoiceReportCodeMapping.PageName.xiaoyiChatView,
      ps: VoiceReportCodeMapping.PageStatus.pcWindow,
      tp: VoiceReportCodeMapping.ActionType.click,
      mt: VoiceReportCodeMapping.ModuleType.beginnerGuidance,
      ct: 'new_user_guide',
      pos: pos,
      aid: aid,
    };
    return reportParamNew;
  }

  onDismiss?: () => void;

  build() {
    Column() {
      Swiper(this.swiperController) {
        LazyForEach(this.data, (item: MyData) => {
          Column({ space: 8 }) {
            Image($r(item.getImage()))
              .width(352)
              .height(180)
              .draggable(false)
              .margin({ top: 24, left: 24, right: 24 })
            Text($r(item.getText1()))
              .fontWeight(FontWeight.Bold)
              .fontSize(18)
            Text($r(item.getText2()))
              .fontWeight(FontWeight.Regular)
              .fontSize(14)
          }
        })
      }
      .onAnimationEnd(() => {
        VoiceReportNew.reportContentExposure(this.getExposureInfoForReport(this.index));
      })
      .height('100%')
      .cachedCount(1)
      .index($$this.index)
      .interval(4000)
      .loop(false)
      .indicatorInteractive(true)
      .itemSpace(8)
      .indicator(
        new DotIndicator()
          .itemWidth(10)
          .itemHeight(10)
          .selectedItemWidth(15)
          .selectedItemHeight(10)
          .color($r('sys.color.ohos_id_color_component_normal'))
          .selectedColor($r('sys.color.ohos_id_color_component_activated'))
          .bottom(70))
      Row() {
        Flex({ alignItems: ItemAlign.Center, justifyContent: FlexAlign.SpaceBetween }) {
          Button(this.index === 0 ? $r('app.string.beginner_guide_finish'):$r('app.string.beginner_guide_button_prev'), { type: ButtonType.Normal, buttonStyle: ButtonStyleMode.NORMAL })
            .width('50%')
            .height(40)
            .margin({ right: 8 })
            .borderRadius(8)
            .onClick(() => {
              if (this.index === 0) {
                this.beginnerGuideDialog?.close();
                this.onDismiss?.();
                VoiceReportNew.reportContentClick(this.getClickInfoForReport(this.index+1, 'exit_guide'));
              } else {
                VoiceReportNew.reportContentClick(this.getClickInfoForReport(this.index+1, 'Previous'));
                this.swiperController.showPrevious()
              }
            })
          Button(this.index !== 3 ? $r('app.string.beginner_guide_button_next'):$r('app.string.beginner_guide_know'), { type: ButtonType.Normal, buttonStyle: ButtonStyleMode.EMPHASIZED })
            .width('50%')
            .height(40)
            .margin({ left: 8 })
            .borderRadius(8)
            .onClick(() => {
              if (this.index === 3) {
                this.beginnerGuideDialog?.close();
                this.onDismiss?.();
                VoiceReportNew.reportContentClick(this.getClickInfoForReport(this.index+1, 'exit_got'));
              } else {
                this.swiperController.showNext()
                VoiceReportNew.reportContentClick(this.getClickInfoForReport(this.index+1, 'next_step'));
              }
            })
        }
      }
      .padding({ left: 16, right: 16 })
      .justifyContent(FlexAlign.Center)
      .align(Alignment.Bottom)
      .width('100%')
      .position({ bottom: 16 })
    }
  }
}