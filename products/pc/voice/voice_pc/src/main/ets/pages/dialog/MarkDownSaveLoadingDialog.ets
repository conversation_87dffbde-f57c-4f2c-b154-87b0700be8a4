/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2024-2024. All rights reserved.
 */

import lazy { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { STORAGE_KEY_IS_DARK_MODE, MARKDOW_SAVING_LOADING_FLAG } from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { PcCardBackground } from '@vassistant/ui/src/main/ets/components/common/PcCardBackground';

const TAG = `MarkDownSaveLoadingDialog`
@Component
export struct MarkDownSaveLoadingDialog {
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;

  aboutToAppear() {
    Logger.info(TAG, 'aboutToAppear');
  }

  aboutToDisappear() {
    Logger.info(TAG, 'aboutToDisappear');
  }

  build() {
    Column() {
      Row() {
        LoadingProgress()
          .width(20)
          .height(20)
          .margin({
            left: $r('app.float.prompt_vp_8'),
          })
        Text($r('app.string.format_generate'))
          .fontSize('14')
          .fontFamily('HarmonyHeiTi')
          .fontColor($r('sys.color.ohos_id_color_text_primary'))
          .fontWeight(FontWeight.Regular)
          .margin({
            left: $r('app.float.prompt_vp_4'),
            right: $r('app.float.prompt_vp_8'),
            top: $r('app.float.prompt_vp_8'),
            bottom: $r('app.float.prompt_vp_8')
          })
          .textOverflow({ overflow: TextOverflow.Ellipsis });
      }
      .alignItems(VerticalAlign.Center)
    }.height(36)
    .backgroundColor(this.isDarkMode ? '#66000000' : $r('sys.color.ohos_id_blur_style_component_regular_color'))
    .borderRadius(8)
    .border({
      color : this.isDarkMode ? '#33ffffff' : '#1A000000',
      width: '1vp'
    })
    .backgroundBlurStyle(BlurStyle.COMPONENT_ULTRA_THICK)
    .shadow(ShadowStyle.OUTER_DEFAULT_MD)
    .alignItems(HorizontalAlign.Center)
  }

}

