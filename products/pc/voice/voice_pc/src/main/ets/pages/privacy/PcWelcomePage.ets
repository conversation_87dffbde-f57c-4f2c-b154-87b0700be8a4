/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2022-2023. All rights reserved.
 */

import common from '@ohos.app.ability.common';
import Want from '@ohos.app.ability.Want';
import { BusinessError } from '@ohos.base';
import { AppProfile } from '@hms-assistant/common-corebase/src/main/ets/constant/AppProfile';
import { AppConstants } from '@hms-assistant/common-corebase/src/main/ets/constant/AppConstants';
import { Logger } from '@hms-assistant/common-corebase/src/main/ets/util/Logger';
import { PermissionManager } from '@hms-assistant/common-corebase/src/main/ets/util/PermissionManager';
import { SafeJson } from '@hms-security/agoh-base-sdk';
import UIExtensionContentSession from '@ohos.app.ability.UIExtensionContentSession';
import { AiDispatchImpl } from '@hms-assistant/common-appservice/src/main/ets/impl/AiDispatchImpl';
import {
  STORAGE_KEY_IS_DARK_MODE,
  STORAGE_KEY_IS_DIRECTION_HORIZONTAL,
  STORAGE_KEY_SETTING_FROM_ABILITY,
  STORAGE_KEY_SETTING_PATH,
  STORAGE_KEY_VOICE_FIX_PATH,
  STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE,
  PRIVACY_VERSION,
  STORAGE_KEY_START_REASON,
  STORAGE_KEY_SETTING_LOAD_PAGE,
  STORAGE_KEY_VOICE_PATH,
  PC_XIAOYI_MODE
} from '@hms-assistant/common-storage/src/main/ets/keys/StorageKey';
import { FromType, KEY_FROM, KEY_SOURCE_ID } from '@hms-assistant/voice-voicebusiness/src/main/ets/utils/WelcomeConstants';
import { PafSpan, PafText } from '@hms-paf/ui-widget-base/src/main/ets/model/PafText';
import { AbilityContextType } from '@hms-paf/ui-widget-base/src/main/ets/common/PafUiWidget';
import SystemSetting from '@hms-assistant/common-storage/src/main/ets/settings/SystemSetting';
import { systemSettingKey } from '@hms-assistant/common-storage/src/main/ets/keys/SystemSettingKey';
import { PafPopPermissionCancel } from '@hms-paf/ui-widget-permission/src/main/ets/components/PafPopPermissionCancel';
import { NavigationUtils, PageParam } from '@hms-assistant/voice-phonebase/src/main/ets/util/NavigationUtils';
import { PrivacyManager } from '@hms-assistant/common-privacy/src/main/ets/manager/PrivacyManager';
import { VoiceRouter } from '@hms-assistant/common-corebase/src/main/ets/frame/VoiceRouter';
import { privacyConstant } from '@hms-assistant/common-storage/src/main/ets/files/PrivacyRecord';
import { kitModel } from '@hms-assistant/voice-voicebusiness/src/main/ets/model/KitModel';
import { AbilityUtils } from '@hms-assistant/voice-voicebusiness/src/main/ets/utils/AbilityUtils';
import { WakeupUtils } from '@hms-assistant/common-corebase/src/main/ets/util/WakeupUtils';
import { Content, ContentData, Header } from '@hms-assistant/common-appservice/src/main/ets/bean/AiDispatchBean';
import { VoiceParam } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceParam';
import { VoiceConst } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceConst';
import { VoiceReport } from '@hms-assistant/voice-phonebase/src/main/ets/report/VoiceReport';
import { PafPermission } from '@hms-paf/ui-widget-permission/src/main/ets/components/PafPermission';
import { VaCommonDialog } from '@vassistant/ui/src/main/ets/dialog/VaCommonDialog';
import { SheetShowConfig } from '@hms-paf/ui-widget-base/src/main/ets/common/PafSheetWindow';
import { PafWebViewController } from '@hms-paf/ui-widget-webview/src/main/ets/api/PafWebViewController';
import { PafPermissionItem, PafPopPermissionDesc } from '@hms-paf/ui-widget-permission';
import { ToastUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ToastUtils';
import { ResourceUtils } from '@hms-assistant/common-corebase/src/main/ets/util/ResourceUtils';
import { PrivacyAgreementPage } from './PrivacyAgreementPage';
import { StandardTagPaf } from '@hms-paf/ui-widget-base/src/main/ets/model/InAppItemModel';
import {
  MainNavPageRegistry,
  NavPageRegisterOptions,
  PafLifeCycle
} from '@hms-paf/ui-widget-base/src/main/ets/common/PafNavPathRegister';
import { SettingUtils } from '@hms-assistant/voice-voicebusiness/src/main/ets/utils/SettingUtils';
import settings from '@ohos.settings';
import { ProtocolIDConst } from '@hms-assistant/common-privacy/src/main/ets/config/StatementConfig';
import i18n from '@ohos.i18n';
import lazy { PrivacyUtils } from '@hms-assistant/common-privacy/src/main/ets/manager/PrivacyUtils';
import { StartReason } from '@hms-assistant/common-corebase/src/main/ets/constant/Constants';
import { PcConstants } from '../../common/PcConstants';
import display from '@ohos.display';
import { PcStatusBarType } from '../chatpage/PcStatusBarType';
import window from '@ohos.window';
import systemParameterEnhance from '@ohos.systemParameterEnhance';

const TAG = 'WelcomePage';
const WHITE_LIST: string[] =
  ['TranslateToolAbility'];
const FOLD_SCREEN_FLAG: String = systemParameterEnhance.getSync('const.window.foldscreen.type', '0,0,0,0');

@Builder
export function WelcomePageBuilder(name: string, param: Object) {
  PcWelcomePage()
}

/**
 * 欢迎页（权限启动页）
 */
@Component
export struct PcWelcomePage {
  private storage: LocalStorage = LocalStorage.getShared();
  sheetShowConfig: SheetShowConfig = new SheetShowConfig();
  pathInfos: NavPathStack = new NavPathStack();
  @Provide('Paf.WebViewController') controller: PafWebViewController = new PafWebViewController()
  @StorageProp(STORAGE_KEY_SETTING_FROM_ABILITY) fromAbility: string = '';
  @StorageProp('Paf.Language') @Watch('getDeclare') private language: string = i18n.System.getSystemLanguage()
  /**
   * 再次确认过，LocalStorageProp当前必须传入实际字符不能传入常量；TODO 提单确认
   */
  @LocalStorageProp(KEY_SOURCE_ID) sourceId: string = '';
  @LocalStorageProp(KEY_FROM) from: string = FromType.FROM_SETTINGS;
  @StorageProp(STORAGE_KEY_START_REASON) reason: string = StartReason.DEFAULT;
  @LocalStorageProp('key_params') sourceParams: Record<string, Object> | undefined = undefined;
  @StorageProp(STORAGE_KEY_IS_DIRECTION_HORIZONTAL) @Watch('horizontal') isHorizontal: boolean = false;
  @StorageProp(STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE) @Watch('isShown') isShow: boolean = false;
  @StorageProp(STORAGE_KEY_IS_DARK_MODE) isDarkMode: boolean = false;
  @State isShowPmsTipsDialog: boolean = false;
  @State isShowExtraServiceDialog: boolean = false;
  @State isShowPmsDesDialog: boolean = false;
  private context: common.UIAbilityContext = getContext(this) as common.UIAbilityContext;
  private permissionDescItems: PafPermissionItem[] = [
    new PafPermissionItem($r('app.string.permission_location'), $r('app.string.permission_location_desc')),
    new PafPermissionItem($r('app.string.permission_photo'), $r('app.string.permission_photo_desc')),
    new PafPermissionItem($r('app.string.permission_microphone'), $r('app.string.permission_microphone_desc')),
    new PafPermissionItem($r('app.string.permission_calendar'), $r('app.string.permission_calendar_desc')),
    new PafPermissionItem($r('app.string.permission_picture'), $r('app.string.permission_picture_desc')),
    new PafPermissionItem($r('app.string.bluetooth'), $r('app.string.bluetooth_desc')),
  ]
  @State declares: Array<PafText> = []
  @State dialogFullDesc: Array<PafText> = []
  @State dialogBasicDesc: Array<PafText> = []
  @State basicTitle: Resource | string = ''
  @State lastFoldStatus: number = 0;
  @State @Watch('onDirectionChange') hopperOrientation: display.Orientation =
    display.getDefaultDisplaySync().orientation;
  @State statusBarType: number = 0x0080;
  @State fullTitle: Resource | string = ''
  pcPermissionCancelDlg: CustomDialogController = new CustomDialogController({
    builder: PafPopPermissionCancel({
      abilityContextType: this.fromAbility === AppProfile.Ability.PC_MAIN_SETTING ||
        this.fromAbility === AppProfile.Ability.DUOLA_EXT_ABILITY ? AbilityContextType.WindowProxy :
      AbilityContextType.WindowStage,
      basicTitle: this.basicTitle,
      fullTitle: this.fullTitle,
      fullDesc: this.dialogFullDesc,
      basicDesc: this.dialogBasicDesc,
      exitAppCustom: true,
      fullService: () => {
        Logger.info(TAG, 'PafAgreement popPermissionCancel fullService');
        this.pcPermissionCancelDlg.close();
        this.agreePrivacy();
      },
      basicService: () => {
        this.pcPermissionCancelDlg.close();
        SystemSetting.setValue(this.context, systemSettingKey.FUSION_BASIC_SERVICE_MODE, AppConstants.ON);
        this.rejectPrivacy(true);
      },
      exit: () => {
        this.pcPermissionCancelDlg.close();
        this.rejectPrivacy(false);
      }
    }),
    alignment: DialogAlignment.Center,
    autoCancel: true,
    customStyle: false
  })
  pcPermissionDescDlg: CustomDialogController = new CustomDialogController({
    builder: PafPopPermissionDesc({
      abilityContextType: this.fromAbility === AppProfile.Ability.MAIN_SETTING ||
        this.fromAbility === AppProfile.Ability.VOICE_FIX_SETTING_ABILITY ||
        this.fromAbility === AppProfile.Ability.DUOLA_EXT_ABILITY ? AbilityContextType.WindowProxy :
      AbilityContextType.WindowStage,
      title: $r('app.string.permission_dialog_title_new'),
      desc: $r('app.string.permission_dialog_description_content_new', this.permissionDescItems.length,
        $r('app.string.main_profile_setting'), $r('app.string.privacy_and_security')),
      items: this.permissionDescItems
    }),
    alignment: DialogAlignment.Center,
    autoCancel: true,
    customStyle: false
  })

  showPermissionDesc() {
    this.pcPermissionDescDlg.open()
  }

  @Builder
  PrivacyAgreementPageBuilder(name: string, param: object, pafLifeCycle: PafLifeCycle) {
    PrivacyAgreementPage({
      isNav: true,
      sheetShowConfig: this.sheetShowConfig,
      pafLife: pafLifeCycle,
      hideTitleBar: false,
      agreementType: (param as Record<string, number>).agreementType,
      agreementTag: (param as Record<string, StandardTagPaf>).agreementTag,
    })
  }

  horizontal() {
    Logger.info(TAG, 'isHorizontal:' + this.isHorizontal);
  }

  private isShown() {
    Logger.info(TAG, 'isShown');
    if (this.isShow) {
      this.onNavBarShow()
    } else {
      this.onNavBarHide()
    }
  }

  private onNavBarShow() {
    Logger.info(TAG, 'onNavBarShow: ' + this.from);
  }

  private onNavBarHide() {
    Logger.info(TAG, 'onNavBarHide');
  }

  private toUserAgreement(): void {
    Logger.info(TAG, 'toUserAgreement');
    ToastUtils.showToast(ResourceUtils.getResourceStringSync($r('app.string.not_supported_capability')));
  }

  private showExtraServiceDialog(): void {
    Logger.info(TAG, 'ShowExtraServiceDialog');
    ToastUtils.showToast(ResourceUtils.getResourceStringSync($r('app.string.no_support_text')));
  }

  private toPrivacyStatement(dialogDeclare?: boolean): void {
    Logger.info(TAG, 'toPrivacyStatement');
    this.pcPermissionCancelDlg?.close();
    this.sheetShowConfig?.getPathStacks()?.pushPathByName('PrivacyAgreementPage', {
      'agreementType': ProtocolIDConst.VOICE_ASSISTANT_SERVER_PRIVACY_STATEMENT_PC,
      'agreementTag': StandardTagPaf.DEFAULT,
    } as Record<string, number | StandardTagPaf>)
  }

  private showPmsDescription(): void {
    Logger.info(TAG, 'showPmsDescription');
    ToastUtils.showToast(ResourceUtils.getResourceStringSync($r('app.string.no_support_text')));
  }

  initSheetConfig() {
    this.sheetShowConfig.setPathStacks(this.pathInfos)
      .setSheetShow(true)
      .setIsNav(true)
      .setShowClose(true)
      .setOnReady((ctx) => {
        this.pathInfos = ctx.navDestinationContext?.pathStack ?? new NavPathStack()
      })
  }

  aboutToAppear() {
    this.getDeclare();
    this.initSheetConfig();
    let localPrivacyWebPage: NavPageRegisterOptions =
      {
        navPathName: 'PrivacyAgreementPage',
        pageTitle: '',
        customStyle: true,
        builder: this.PrivacyAgreementPageBuilder.bind(this)
      }
    MainNavPageRegistry.getMainNavPageRegistry()!.register([localPrivacyWebPage]);
    this.statusBarType = PcStatusBarType.readStatusBarType(this.context);
    if (FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER) {
      display.on('foldStatusChange', () => this.onDirectionChange())
      display.on('change', () => {
        this.updateCurrentDirection()
      });
      this.lastFoldStatus = display.getFoldStatus()
      this.hopperOrientation = display.getDefaultDisplaySync().orientation
    }
  }

  getDeclare() {
    this.declares = [
      new PafText()
        .format($r('app.string.welcome_privacy_agree_tips_pc_new'), [
          new PafSpan($r('app.string.privacy')).emphasis().hyperlink(() => {
            this.toPrivacyStatement();
          }),
          new PafSpan($r('app.string.welcome_notice_tips_v10_link_permission')).emphasis().hyperlink(() => {
            this.showPermissionDesc();
          })
        ]),
      new PafText()
        .appendNormal($r('app.string.welcome_privacy_tips_v1_new'))
    ];

    this.dialogFullDesc = [
      new PafText()
        .format($r('app.string.welcome_privacy_tips_new_pc'), [
          new PafSpan($r('app.string.privacy')).emphasis().hyperlink(() => {
            this.toPrivacyStatement(true);
          }),
          new PafSpan($r('app.string.welcome_notice_tips_v10_link_permission')).emphasis().hyperlink(() => {
            this.showPermissionDesc();
          })
        ]),
      new PafText()
        .appendNormal($r('app.string.welcome_notice_tips_v13_full_mode'))]

    this.dialogBasicDesc = [
      new PafText()
        .format($r('app.string.basic_mode_desc_pc'))]

    this.basicTitle = $r('app.string.welcome_notice_tips_basic_mode_opt');
    this.fullTitle = $r('app.string.welcome_notice_tips_full_mode_opt');
  }

  aboutToDisappear(): void {
    Logger.info(TAG, 'aboutToDisappear');
    if (FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER) {
      display.off('foldStatusChange')
      display.off('change')
    }
    this.pcPermissionCancelDlg?.close();
  }

  private agreePrivacy(): void {
    PrivacyManager.getInstance().agree(getContext(this));
    PrivacyUtils.doPafSign(getContext(this));
    PrivacyManager.getInstance().appendPrivacyRecord(getContext(this), true, privacyConstant.TAG_AGREE_CLICK);
    let service = (VoiceRouter.getService('AiDispatch') as AiDispatchImpl);
    service.init(this.context).then(() => {
      service.dispatch(this.buildContent(true, false)).catch(() => {
        Logger.error(TAG, `dispatch fail`);
      });
    });
    let data: object[] = [{
      'settingId': 'privacyStatus',
      'action': '1'
    } as Record<string, string>]

    this.context.startAbility({
      bundleName: AppProfile.BundleName.CELIA_SEARCH_BUNDLE_NAME,
      abilityName: AppProfile.Ability.CELIA_SEARCH_ABILITY,
      parameters: {
        settingAction: SafeJson.ohAegJsonStringify(data),
      }
    }).catch((err: BusinessError) => {
      Logger.error(TAG, `start hivsion service error ${SafeJson.ohAegJsonStringify(err)}.`);
    });
    if (this.from === 'other') {
      this.context.terminateSelf();
      return;
    }
    if (this.from === FromType.FROM_CELIA_CALL || this.from === FromType.FROM_VERDE_LIVE) {
      this.context.terminateSelfWithResult({
        resultCode: 0
      });
      return;
    }
    if (this.from === FromType.FROM_TRANSLATE_TOOL) {
      this.startOriginalAbility(this.context);
      return;
    }
    if (this.from === FromType.FROM_VOICE_FIX) {
      NavigationUtils.replace(STORAGE_KEY_VOICE_FIX_PATH, AppProfile.SubPage.VOICE_FIX_SETTING_PAGE);
      return;
    }
    if (this.from === FromType.FROM_HI_VISION) {
      // HiVision隐私协议同意立即返回
      this.toHiVision(this.context);
      return;
    }
    if (this.from === FromType.FROM_PC_HI_SEARCH) {
      this.toNotifyPcHiSearch(true);
      return;
    }
    if (this.from === FromType.FROM_AI_OFFICE) {
      // AiOffice隐私协议同意立即返回
      AbilityUtils.terminateSelfWithResult(this.context, 200);
      return;
    }
    if (this.from === FromType.FROM_TRANSCRIPTION) {
      AbilityUtils.terminateSelfWithResult(this.context, 200);
      return;
    }
    PermissionManager.checkPermission(PermissionManager.PERMISSIONS_USER_AGENT_NECESSARY)
      .then((isGrant: boolean) => {
        Logger.info(TAG, `check isGrant ${isGrant}`);
        if (!isGrant) {
          this.requestPermission();
          return;
        }
        this.permissionGranted();
      });
    kitModel.aiDataDataSync(this.context);
  }

  private buildContent(isAgree: boolean, isAgreeBasicService: boolean): Content {
    let content: Content = new Content();
    content.contentData = [];
    let contentData: ContentData = new ContentData();
    contentData.header = new Header();
    contentData.header.namespace = 'Event';
    contentData.header.name = 'fusionSwitchChange';
    let sourceParams = this.storage?.get('key_params') as Record<string, Object>;
    let sourceId = sourceParams?.sourceId ?? this.sourceId;
    contentData.payload = {
      fusionAssistantUserExperiencePlanOn: true,
      fusionAssistantPrivacyOn: isAgree,
      fusionBasicServiceModeOn: isAgreeBasicService,
      fusionAssistantPrivacyVersion: PRIVACY_VERSION,
      sourceId: sourceId,
    };
    content.contentData.push(contentData);
    return content;
  }

  private permissionGranted(): void {
    Logger.info(TAG, `from ${this.from}`);
    if (this.from === FromType.FROM_CHAT) {
      this.toChatPage();
    } else if (this.from === FromType.FROM_SETTINGS) {
      this.toWakeUpSetting();
    } else if (this.from === FromType.FROM_SETTINGS_TIMBRE) {
      this.toTimbreSetting();
    } else if (this.from === FromType.FROM_AI_CAPTION_SETTING) {
      this.toAiCaptionSetting();
    } else if (this.from === FromType.FROM_SETTING_HI_SEARCH) {
      this.toHiSearch();
    } else if (this.reason !== StartReason.DEFAULT &&
      this.storage.get<string>(STORAGE_KEY_SETTING_LOAD_PAGE) !== AppProfile.SubPage.MAIN_SETTING_PAGE) {
      // 搜索进入
      NavigationUtils.replace(STORAGE_KEY_SETTING_PATH, this.from, { 'isFirst': true } as Record<string, boolean>);
    } else if (this.from === FromType.FROM_SETTINGS) {
      // 设置进入
      AbilityUtils.terminateSelf(this.context);
      WakeupUtils.openHalfWindow(this.context);
    } else if (this.from === FromType.FROM_POWER_GUIDE) {
      this.toPowerGuide(this.context);
    } else if (this.from === FromType.FROM_AI_NAVIGATION_BAR) {
      AbilityUtils.terminateSelf(this.context);
      WakeupUtils.openHalfWindow(this.context);
    } else {
      Logger.warn(TAG, 'not support from type ' + this.from);
      AbilityUtils.terminateSelf(this.context);
    }
  }

  private requestPermission(): void {
    PermissionManager.requestPermission(this.context, PermissionManager.PERMISSIONS_USER_AGENT_NECESSARY)
      .then((result: boolean) => {
        if (result) {
          Logger.debug(TAG, 'requestPermission success');
          this.permissionGranted();
        } else {
          this.isShowPmsTipsDialog = true;
          Logger.error(TAG, 'requestPermission failed');
        }
      });
  }

  private toWakeUpSetting(): void {
    // 设置项唤醒进入
    Logger.info(TAG, `toWakeUpSetting`);
    NavigationUtils.replace(STORAGE_KEY_SETTING_PATH, AppProfile.SubPage.WAKEUP_SETTING);
  }

  private toTimbreSetting(): void {
    // 设置项音色进入
    Logger.info(TAG, `toTimbreSetting`);
    NavigationUtils.replace(STORAGE_KEY_SETTING_PATH, AppProfile.SubPage.TIMBRE_SETTINGS_PAGE);
  }

  private toAiCaptionSetting(): void {
    // 设置项小艺字幕进入
    Logger.info(TAG, `toAiCaptionSetting`);
    NavigationUtils.replace(STORAGE_KEY_SETTING_PATH, AppProfile.SubPage.AI_CAPTION_SETTING);
  }

  private toHiVision(myContext: common.UIAbilityContext): void {
    let want: Want = {
      bundleName: this.sourceParams?.serviceBundleName as string,
      abilityName: this.sourceParams?.serviceAbilityName as string,
      parameters: this.sourceParams?.originParameters as Record<string, Object>
    } as Want;
    myContext.startServiceExtensionAbility(want).then(() => {
      AbilityUtils.terminateSelf(myContext);
    }).catch((err: BusinessError) => {
      Logger.error(TAG, `start hivsion service error ${SafeJson.ohAegJsonStringify(err)}.`);
    });
  }

  private toHiSearch(): void {
    NavigationUtils.replace(STORAGE_KEY_SETTING_PATH, AppProfile.SubPage.CELIA_SEARCH_PAGE);
  }

  private toChatPage(): void {
    Logger.error(TAG, `toChatPage`);
    if (AppStorage.get(PC_XIAOYI_MODE) === 'sideBarMode') {
      NavigationUtils.setVoiceLoadPage(AppProfile.SubPage.PC_SIDEBAR_PAGE);
    } else {
      NavigationUtils.setVoiceLoadPage(AppProfile.SubPage.CHAT_MAIN);
    }

    NavigationUtils.push(STORAGE_KEY_VOICE_PATH, AppProfile.SubPage.CHAT_MAIN_PC, new PageParam(), false);
    AppStorage.setOrCreate(STORAGE_KEY_VOICE_PAGE_SHOWN_STAGE, false);
  }

  private toPowerGuide(myContext: common.UIAbilityContext): void {
    AbilityUtils.startAbility(myContext, AppProfile.Ability.SETTING, AppProfile.SubPage.WAKEUP_FINISH).then(() => {
      AbilityUtils.terminateSelf(myContext);
    });
  }

  private isAllowJump(): boolean {
    let sourceParams = this.storage?.get('key_params') as Record<string, Object>;
    if ((sourceParams?.serviceBundleName as string) === AppProfile.BundleName.CELIA_BUNDLE_NAME &&
    WHITE_LIST.includes(sourceParams?.serviceAbilityName as string)) {
      Logger.info(TAG, 'allowjump');
      return true;
    } else {
      Logger.info(TAG, 'reject');
      return false;
    }
  }

  private startOriginalAbility(myContext: common.UIAbilityContext | common.UIExtensionContext): void {
    if (this.isAllowJump()) {
      myContext.startAbility(this.getOriginalWant()).then(() => {
        AbilityUtils.terminateSelf(myContext);
      }).catch((err: BusinessError) => {
        Logger.error(TAG, `startOriginalAbility error ${SafeJson.ohAegJsonStringify(err)}.`);
      });
    } else {
      Logger.info(TAG, 'not allow and terminateSelf');
      AbilityUtils.terminateSelf(myContext);
    }
  }

  private getOriginalWant() {
    let sourceParams = this.storage?.get('key_params') as Record<string, Object>;
    return {
      bundleName: sourceParams?.serviceBundleName as string,
      abilityName: sourceParams?.serviceAbilityName as string,
      parameters: sourceParams?.originParameters as Record<string, Object>,
      uri: sourceParams?.originalUri as string
    } as Want;
  }

  private rejectPrivacy(isAgreeBasicService: boolean): void {
    Logger.info(TAG, 'rejectPrivacy');
    if (isAgreeBasicService) {
      SettingUtils.initPersonalLabels(getContext(this));
    }
    const service = (VoiceRouter.getService('AiDispatch') as AiDispatchImpl);
    service.init(this.context).then(() => {
      service.dispatch(this.buildContent(false, isAgreeBasicService)).catch(() => {
        Logger.error(TAG, `dispatch fail`);
      });
    });
    if (this.from === FromType.FROM_SETTING_HI_SEARCH && isAgreeBasicService) {
      // 设置搜索进入
      NavigationUtils.replace(STORAGE_KEY_SETTING_PATH, AppProfile.SubPage.CELIA_SEARCH_PAGE,
        { 'pageName': 'settingPage' } as Record<string, string>);
      return;
    }
    this.rejectPrivacyOfBasic();
  }

  /**
   * 通知全搜是否同意协议
   */
  private toNotifyPcHiSearch(isAgree: boolean): void {
    try {
      let session = this.storage.get<UIExtensionContentSession>('session');
      session?.sendData({
        'event': isAgree ? 'agreePrivacy' : 'rejectPrivacy'
      });
    } catch (error) {
      let err: BusinessError = error as BusinessError;
      Logger.error(TAG, `notify hisearch failed, code is ${err?.code}, message is ${err?.message}`);
    }
  }

  private rejectPrivacyOfBasic(): void {
    if (this.from === 'other') {
      this.context.terminateSelf();
      return;
    }
    if (this.from === FromType.FROM_CELIA_CALL || this.from === FromType.FROM_VERDE_LIVE) {
      this.context.terminateSelfWithResult({
        resultCode: -1
      });
      return;
    }
    if (this.from === FromType.FROM_TRANSLATE_TOOL) {
      this.context.terminateSelfWithResult({
        resultCode: -1
      });
      return;
    }
    if (this.from === FromType.FROM_AI_OFFICE) {
      AbilityUtils.terminateSelfWithResult(this.context, 100);
      return;
    }
    if (this.from === FromType.FROM_TRANSCRIPTION) {
      AbilityUtils.terminateSelfWithResult(this.context, 100);
      return;
    }
    if (this.from === FromType.FROM_PC_HI_SEARCH) {
      this.toNotifyPcHiSearch(false);
      return;
    }
    if (this.fromAbility === AppProfile.Ability.PC_MAIN_SETTING) {
      if (this.reason !== StartReason.DEFAULT &&
        this.storage.get<string>(STORAGE_KEY_SETTING_LOAD_PAGE) !== AppProfile.SubPage.MAIN_SETTING_PAGE) {
        NavigationUtils.clear(STORAGE_KEY_SETTING_PATH);
        NavigationUtils.push(STORAGE_KEY_SETTING_PATH, AppProfile.SubPage.MAIN_SETTING_PAGE, {
          'isFirst': true
        } as Record<string, boolean>);
      } else {
        NavigationUtils.pop(STORAGE_KEY_SETTING_PATH);
      }
    } else {
      AbilityUtils.terminateSelf(this.context);
    }
  }

  private reportDialogEnter(): void {
    let param: VoiceParam.DialogEnterParam = {
      option: VoiceConst.Option.WELCOME_PAGE,
      enterType: VoiceConst.EnterType.APP,
      result: VoiceConst.Result.SUCCESS
    }
    VoiceReport.reportDialogEnter(param);
  }

  @Builder
  payloadBuilder() {
    PafPermission({
      // fullPage: true,
      abilityContextType: (this.fromAbility === AppProfile.Ability.MAIN_SETTING ||
        this.fromAbility === AppProfile.Ability.DUOLA_EXT_ABILITY) ? AbilityContextType.WindowProxy :
      AbilityContextType.WindowStage,
      appImg: $r('app.media.icon'),
      appName: $r('app.string.voice_assistant_name_new'),
      appDesc: $r('app.string.welcome_tips_new'),
      permissionDeclare: $declares,
      sheetShowConfig: this.sheetShowConfig,
      alwaysDarkMode: false,
      confirm: () => {
        kitModel.initVoicekitSdk();
        this.agreePrivacy();
      },
      cancel: () => {
        // 弹挽留页
        let isBasicAgree =
          SystemSetting.getSettingsValue(this.context, systemSettingKey.FUSION_BASIC_SERVICE_MODE, AppConstants.OFF,
            settings.domainName.USER_PROPERTY);
        Logger.info(TAG, 'basic mode ' + isBasicAgree);
        if (isBasicAgree === AppConstants.OFF) {
          this.pcPermissionCancelDlg.open()
        } else {
          this.rejectPrivacyOfBasic()
        }
      },
    })
      .id('welcome_page.payload_builder.paf_permission')
  }

  build() {
    NavDestination() {
      this.payloadBuilder();
      VaCommonDialog({
        visible: this.isShowPmsTipsDialog,
        content: $r('app.string.missing_permission'),
        confirm: {
          text: $r('app.string.voice_confirm')
        },
        dismiss: () => {
          if (this.fromAbility === AppProfile.Ability.PC_MAIN_SETTING) {
            NavigationUtils.pop(STORAGE_KEY_SETTING_PATH);
          } else if (this.fromAbility === AppProfile.Ability.VOICE) {
            AbilityUtils.terminateSelf(this.context);
          } else if (this.fromAbility === AppProfile.Ability.VOICE_FIX_SETTING_ABILITY) {
            NavigationUtils.pop(STORAGE_KEY_VOICE_FIX_PATH);
            (this.storage.get('session') as UIExtensionContentSession)?.sendData({ 'action': 'backpressed' });
          }
        }
      }).id('pc_welcome_page.build.common_dialog');
    }
    .hideTitleBar(true)
    .onShown(() => {
      Logger.info(TAG, 'onShown:' + this.from);
      VoiceReport.setPageInfo(TAG, VoiceConst.PageStatus.FULL_SCREEN);
      this.reportDialogEnter();
    })
    .onHidden(() => {
      Logger.info(TAG, 'onHidden');
      VoiceReport.reportDialogExit(VoiceConst.Option.WELCOME_PAGE);
    })
    .onBackPressed(() => {
      return false;
    })
    .onReady((context: NavDestinationContext) => {
      if (!this.sheetShowConfig) {
        this.pathInfos = context.pathStack
      }
    })
  }

  async onDirectionChange() {
    Logger.info(TAG, 'onDirectionChange');
    if (display.getFoldStatus() === display.FoldStatus.FOLD_STATUS_EXPANDED &&
      this.lastFoldStatus !== display.FoldStatus.FOLD_STATUS_EXPANDED) {
      this.lastFoldStatus = display.FoldStatus.FOLD_STATUS_EXPANDED
      return
    }
    this.lastFoldStatus = display.getFoldStatus()
    if (display.getFoldStatus() !== display.FoldStatus.FOLD_STATUS_EXPANDED) {
      return
    }
    let WINDOW_LEFT_MARGIN: number =
      FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER ? PcConstants.HPR_WINDOW_LEFT_MARGIN :
      PcConstants.HAD_WINDOW_LEFT_MARGIN;
    let WINDOW_TOP_OFFSET: number =
      FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER ? PcConstants.HPR_WINDOW_TOP_OFFSET :
      PcConstants.HAD_WINDOW_TOP_OFFSET;
    let MAC_TOP_MARGIN: number =
      FOLD_SCREEN_FLAG === PcConstants.IS_HOPPER ? PcConstants.HPR_MAC_TOP_MARGIN : PcConstants.HAD_MAC_TOP_MARGIN
    let displayInfo: display.Display = display.getDefaultDisplaySync();
    let isMacStyle = PcStatusBarType.isMacStyle(this.statusBarType)
    let offsetTop: number = 0;
    if (displayInfo.orientation === display.Orientation.PORTRAIT ||
      displayInfo.orientation === display.Orientation.PORTRAIT_INVERTED) {
      let height = isMacStyle ?
        displayInfo.height / 2 - PcConstants.DOCK_HEIGHT - WINDOW_TOP_OFFSET -
        PcConstants.MIDDLE_SIGN / 2 -
        MAC_TOP_MARGIN - PcConstants.HPR_TOP_DOCK_HEIGHT :
        displayInfo.height / 2 - PcConstants.DOCK_HEIGHT - PcConstants.MIDDLE_SIGN / 2 -
        WINDOW_TOP_OFFSET - WINDOW_TOP_OFFSET
      offsetTop = displayInfo.height - PcConstants.DOCK_HEIGHT - WINDOW_TOP_OFFSET - height
    } else {
      let height = isMacStyle ?
        displayInfo.width / 2 - PcConstants.DOCK_HEIGHT - WINDOW_TOP_OFFSET -
        PcConstants.MIDDLE_SIGN / 2 -
        MAC_TOP_MARGIN - PcConstants.HPR_TOP_DOCK_HEIGHT :
        displayInfo.width / 2 - PcConstants.DOCK_HEIGHT - PcConstants.MIDDLE_SIGN / 2 -
        WINDOW_TOP_OFFSET - WINDOW_TOP_OFFSET
      offsetTop = displayInfo.height - PcConstants.DOCK_HEIGHT - WINDOW_TOP_OFFSET - height
    }
    let offsetLeft: number =
      i18n.isRTL(i18n.System.getAppPreferredLanguage()) ? WINDOW_LEFT_MARGIN :
        displayInfo.width - vp2px(PcConstants.WINDOW_WIDTH_INIT) - WINDOW_LEFT_MARGIN;
    let mainWindow: window.Window = await window.getLastWindow(getContext())
    setTimeout(() => {
      mainWindow.moveWindowTo(offsetLeft, offsetTop)
    }, 50)
  }

  private updateCurrentDirection() {
    let displayOrientation: display.Orientation = display.getDefaultDisplaySync().orientation;
    Logger.info(TAG, 'updateCurrentDirection: ' + displayOrientation)
    this.hopperOrientation = displayOrientation;
  }
}