
import { PafLifeCycle } from '@hms-paf/ui-widget-base/src/main/ets/common/PafNavPathRegister';
import { SheetShowConfig } from '@hms-paf/ui-widget-base/src/main/ets/common/PafSheetWindow';
import {
  OpenSourceLicensePageComponent
} from '@vassistant/ui/src/main/ets/components/privacy/OpenSourceLicensePageComponent';
import { SettingNavigationModifier, splitNavigationTitleBuilder } from '../setting/myPage/SettingNavigationConfig';

@Builder
export function OpenSourceLicensePageBuilder(name: string, param: Object) {
  OpenSourceLicensePage({ srcUri: (param as Record<string, string>)?.uri })
}

/**
 * 小艺开源许可页
 *
 * <AUTHOR>
 * @since 2024-06-11
 */
@Component
export struct OpenSourceLicensePage {
  pafLife?: PafLifeCycle = undefined;
  sheetShowConfig?: SheetShowConfig = undefined;
  srcUri: string | Resource = '';

  build() {
    NavDestination() {
      OpenSourceLicensePageComponent({ srcUri: this.srcUri, isPc: true });
    }
    .backgroundColor($r('sys.color.ohos_id_color_sub_background'))
    .title(splitNavigationTitleBuilder(''))
    .attributeModifier(new SettingNavigationModifier())
  }
}
