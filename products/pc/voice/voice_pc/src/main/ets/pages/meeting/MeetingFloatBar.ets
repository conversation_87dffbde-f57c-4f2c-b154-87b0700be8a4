/*
 * Copyright (c) Huawei Technologies Co., Ltd. 2025-2025. All rights reserved.
 */
import { RecordCardShowDialog } from '../dialog/RecordCardShowDialog';
import { LengthMetrics } from '@kit.ArkUI';
import { MeetingStub } from './MeetingStub';
import { MeetingState } from './internal/MeetingState';

@Builder
export function MeetingFloatBarBuilder() {
  MeetingFloatBar()
}

@Component
export struct MeetingFloatBar {

  @State state: MeetingState = MeetingStub.getInstance().state

  build() {
    if (!this.state.showExtArea && this.state.liveMeeting) {
      RecordCardShowDialog({})
    }
  }
}